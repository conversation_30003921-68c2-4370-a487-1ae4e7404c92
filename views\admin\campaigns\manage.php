<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-paper-plane"></i> <?php echo _l('email_marketing_campaigns'); ?>
                </h3>
                <div class="panel-options">
                    <a href="<?php echo admin_url('email_marketing/campaigns/campaign'); ?>" class="btn btn-primary">
                        <i class="fa fa-plus"></i> <?php echo _l('email_marketing_new_campaign'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Filters -->
<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-body">
                <div class="campaign-filters">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_filter_status'); ?></label>
                                <select class="form-control selectpicker" id="filter-status" data-none-selected-text="<?php echo _l('email_marketing_all_statuses'); ?>">
                                    <option value=""><?php echo _l('email_marketing_all_statuses'); ?></option>
                                    <option value="draft"><?php echo _l('email_marketing_draft'); ?></option>
                                    <option value="scheduled"><?php echo _l('email_marketing_scheduled'); ?></option>
                                    <option value="sending"><?php echo _l('email_marketing_sending'); ?></option>
                                    <option value="sent"><?php echo _l('email_marketing_sent'); ?></option>
                                    <option value="paused"><?php echo _l('email_marketing_paused'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_filter_type'); ?></label>
                                <select class="form-control selectpicker" id="filter-type" data-none-selected-text="<?php echo _l('email_marketing_all_types'); ?>">
                                    <option value=""><?php echo _l('email_marketing_all_types'); ?></option>
                                    <option value="email"><?php echo _l('email_marketing_email'); ?></option>
                                    <option value="sms"><?php echo _l('email_marketing_sms'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_filter_date_range'); ?></label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" id="filter-date-from" placeholder="<?php echo _l('email_marketing_from'); ?>">
                                    <span class="input-group-addon">-</span>
                                    <input type="text" class="form-control datepicker" id="filter-date-to" placeholder="<?php echo _l('email_marketing_to'); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="btn-group btn-group-justified">
                                    <a class="btn btn-default" onclick="resetFilters()">
                                        <i class="fa fa-refresh"></i> <?php echo _l('email_marketing_reset'); ?>
                                    </a>
                                    <a class="btn btn-primary" onclick="applyFilters()">
                                        <i class="fa fa-filter"></i> <?php echo _l('email_marketing_filter'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaigns Table -->
<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="campaigns-table">
                        <thead>
                            <tr>
                                <th width="20">
                                    <div class="checkbox">
                                        <input type="checkbox" id="select-all">
                                        <label for="select-all"></label>
                                    </div>
                                </th>
                                <th><?php echo _l('email_marketing_campaign_name'); ?></th>
                                <th><?php echo _l('email_marketing_type'); ?></th>
                                <th><?php echo _l('email_marketing_status'); ?></th>
                                <th><?php echo _l('email_marketing_recipients'); ?></th>
                                <th><?php echo _l('email_marketing_sent_date'); ?></th>
                                <th><?php echo _l('email_marketing_open_rate'); ?></th>
                                <th><?php echo _l('email_marketing_click_rate'); ?></th>
                                <th><?php echo _l('email_marketing_actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulk-actions-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo _l('email_marketing_bulk_actions'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label><?php echo _l('email_marketing_select_action'); ?></label>
                    <select class="form-control" id="bulk-action-select">
                        <option value=""><?php echo _l('email_marketing_select_action'); ?></option>
                        <option value="delete"><?php echo _l('email_marketing_delete'); ?></option>
                        <option value="duplicate"><?php echo _l('email_marketing_duplicate'); ?></option>
                        <option value="pause"><?php echo _l('email_marketing_pause'); ?></option>
                        <option value="resume"><?php echo _l('email_marketing_resume'); ?></option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <span id="selected-count">0</span> <?php echo _l('email_marketing_campaigns_selected'); ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()"><?php echo _l('email_marketing_execute'); ?></button>
            </div>
        </div>
    </div>
</div>

<style>
/* Campaign Management Styles */
.campaign-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.campaign-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.campaign-status.draft {
    background: #6c757d;
    color: white;
}

.campaign-status.scheduled {
    background: #007bff;
    color: white;
}

.campaign-status.sending {
    background: #ffc107;
    color: #212529;
}

.campaign-status.sent {
    background: #28a745;
    color: white;
}

.campaign-status.paused {
    background: #dc3545;
    color: white;
}

.campaign-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.campaign-type.email {
    background: #e3f2fd;
    color: #1976d2;
}

.campaign-type.sms {
    background: #f3e5f5;
    color: #7b1fa2;
}

.campaign-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.campaign-actions {
    display: flex;
    gap: 5px;
}

.btn-action {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
}

.btn-edit {
    background: #007bff;
    color: white;
}

.btn-duplicate {
    background: #28a745;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-pause {
    background: #ffc107;
    color: #212529;
}

.btn-resume {
    background: #17a2b8;
    color: white;
}

/* Table enhancements */
#campaigns-table {
    font-size: 14px;
}

#campaigns-table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

#campaigns-table tbody tr {
    transition: all 0.3s ease;
}

#campaigns-table tbody tr:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .campaign-filters .row > div {
        margin-bottom: 15px;
    }
    
    .campaign-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .campaign-actions {
        flex-direction: column;
    }
}

/* Loading animation */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    initCampaignsTable();
    
    // Initialize date pickers
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true
    });
    
    // Select all checkbox handler
    $('#select-all').change(function() {
        $('.campaign-checkbox').prop('checked', this.checked);
        updateBulkActionsButton();
    });
    
    // Individual checkbox handler
    $(document).on('change', '.campaign-checkbox', function() {
        updateBulkActionsButton();
    });
    
    // Bulk actions button
    $('#bulk-actions-btn').click(function() {
        const selectedCount = $('.campaign-checkbox:checked').length;
        $('#selected-count').text(selectedCount);
        $('#bulk-actions-modal').modal('show');
    });
});

function initCampaignsTable() {
    window.campaignsTable = $('#campaigns-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: admin_url + 'email_marketing/campaigns/table_data',
            type: 'POST',
            data: function(d) {
                d.status = $('#filter-status').val();
                d.type = $('#filter-type').val();
                d.date_from = $('#filter-date-from').val();
                d.date_to = $('#filter-date-to').val();
            }
        },
        columns: [
            {
                data: 'id',
                orderable: false,
                render: function(data, type, row) {
                    return `<div class="checkbox">
                        <input type="checkbox" class="campaign-checkbox" value="${data}" id="cb-${data}">
                        <label for="cb-${data}"></label>
                    </div>`;
                }
            },
            {
                data: 'name',
                render: function(data, type, row) {
                    return `<div>
                        <strong><a href="${admin_url}email_marketing/campaigns/campaign/${row.id}">${data}</a></strong>
                        <br><small class="text-muted">${row.subject}</small>
                    </div>`;
                }
            },
            {
                data: 'type',
                render: function(data, type, row) {
                    return `<span class="campaign-type ${data}">${data.toUpperCase()}</span>`;
                }
            },
            {
                data: 'status',
                render: function(data, type, row) {
                    return `<span class="campaign-status ${data}">${data.toUpperCase()}</span>`;
                }
            },
            {
                data: 'recipients_count',
                render: function(data, type, row) {
                    return `<div class="text-center">
                        <div class="stat-value">${data || 0}</div>
                        <div class="stat-label">Recipients</div>
                    </div>`;
                }
            },
            {
                data: 'sent_at',
                render: function(data, type, row) {
                    if (data) {
                        return moment(data).format('MMM DD, YYYY HH:mm');
                    }
                    return row.scheduled_at ? 
                        '<span class="text-info">Scheduled: ' + moment(row.scheduled_at).format('MMM DD, YYYY HH:mm') + '</span>' :
                        '<span class="text-muted">Not sent</span>';
                }
            },
            {
                data: 'open_rate',
                render: function(data, type, row) {
                    const rate = parseFloat(data || 0);
                    return `<div class="text-center">
                        <div class="stat-value">${rate.toFixed(1)}%</div>
                        <div class="stat-label">${row.opens_count || 0} opens</div>
                    </div>`;
                }
            },
            {
                data: 'click_rate',
                render: function(data, type, row) {
                    const rate = parseFloat(data || 0);
                    return `<div class="text-center">
                        <div class="stat-value">${rate.toFixed(1)}%</div>
                        <div class="stat-label">${row.clicks_count || 0} clicks</div>
                    </div>`;
                }
            },
            {
                data: 'id',
                orderable: false,
                render: function(data, type, row) {
                    let actions = `<div class="campaign-actions">`;
                    
                    // Edit button
                    actions += `<button class="btn-action btn-edit" onclick="editCampaign(${data})" title="Edit">
                        <i class="fa fa-edit"></i>
                    </button>`;
                    
                    // Duplicate button
                    actions += `<button class="btn-action btn-duplicate" onclick="duplicateCampaign(${data})" title="Duplicate">
                        <i class="fa fa-copy"></i>
                    </button>`;
                    
                    // Status-specific actions
                    if (row.status === 'draft') {
                        actions += `<button class="btn-action btn-edit" onclick="sendCampaign(${data})" title="Send">
                            <i class="fa fa-paper-plane"></i>
                        </button>`;
                    } else if (row.status === 'sending') {
                        actions += `<button class="btn-action btn-pause" onclick="pauseCampaign(${data})" title="Pause">
                            <i class="fa fa-pause"></i>
                        </button>`;
                    } else if (row.status === 'paused') {
                        actions += `<button class="btn-action btn-resume" onclick="resumeCampaign(${data})" title="Resume">
                            <i class="fa fa-play"></i>
                        </button>`;
                    }
                    
                    // Analytics button for sent campaigns
                    if (row.status === 'sent') {
                        actions += `<button class="btn-action btn-edit" onclick="viewAnalytics(${data})" title="Analytics">
                            <i class="fa fa-bar-chart"></i>
                        </button>`;
                    }
                    
                    // Delete button
                    actions += `<button class="btn-action btn-delete" onclick="deleteCampaign(${data})" title="Delete">
                        <i class="fa fa-trash"></i>
                    </button>`;
                    
                    actions += `</div>`;
                    return actions;
                }
            }
        ],
        order: [[5, 'desc']], // Order by sent date
        pageLength: 25,
        responsive: true,
        language: {
            emptyTable: "No campaigns found",
            processing: '<div class="loading-spinner"></div>'
        }
    });
}

function applyFilters() {
    window.campaignsTable.ajax.reload();
}

function resetFilters() {
    $('#filter-status').val('').selectpicker('refresh');
    $('#filter-type').val('').selectpicker('refresh');
    $('#filter-date-from').val('');
    $('#filter-date-to').val('');
    window.campaignsTable.ajax.reload();
}

function updateBulkActionsButton() {
    const selectedCount = $('.campaign-checkbox:checked').length;
    
    if (selectedCount > 0) {
        if (!$('#bulk-actions-btn').length) {
            $('.panel-options').append(`
                <button class="btn btn-warning btn-sm" id="bulk-actions-btn" style="margin-left: 10px;">
                    <i class="fa fa-cogs"></i> Bulk Actions (${selectedCount})
                </button>
            `);
            
            $('#bulk-actions-btn').click(function() {
                $('#selected-count').text(selectedCount);
                $('#bulk-actions-modal').modal('show');
            });
        } else {
            $('#bulk-actions-btn').html(`<i class="fa fa-cogs"></i> Bulk Actions (${selectedCount})`);
        }
    } else {
        $('#bulk-actions-btn').remove();
    }
}

function editCampaign(id) {
    window.location.href = admin_url + 'email_marketing/campaigns/campaign/' + id;
}

function duplicateCampaign(id) {
    if (confirm('Are you sure you want to duplicate this campaign?')) {
        $.post(admin_url + 'email_marketing/campaigns/duplicate', {id: id}, function(response) {
            if (response.success) {
                alert_float('success', 'Campaign duplicated successfully');
                window.campaignsTable.ajax.reload();
            } else {
                alert_float('danger', response.message || 'Failed to duplicate campaign');
            }
        });
    }
}

function deleteCampaign(id) {
    if (confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
        $.post(admin_url + 'email_marketing/campaigns/delete', {id: id}, function(response) {
            if (response.success) {
                alert_float('success', 'Campaign deleted successfully');
                window.campaignsTable.ajax.reload();
            } else {
                alert_float('danger', response.message || 'Failed to delete campaign');
            }
        });
    }
}

function sendCampaign(id) {
    if (confirm('Are you sure you want to send this campaign?')) {
        $.post(admin_url + 'email_marketing/campaigns/send', {id: id}, function(response) {
            if (response.success) {
                alert_float('success', 'Campaign sent successfully');
                window.campaignsTable.ajax.reload();
            } else {
                alert_float('danger', response.message || 'Failed to send campaign');
            }
        });
    }
}

function pauseCampaign(id) {
    $.post(admin_url + 'email_marketing/campaigns/pause', {id: id}, function(response) {
        if (response.success) {
            alert_float('success', 'Campaign paused successfully');
            window.campaignsTable.ajax.reload();
        } else {
            alert_float('danger', response.message || 'Failed to pause campaign');
        }
    });
}

function resumeCampaign(id) {
    $.post(admin_url + 'email_marketing/campaigns/resume', {id: id}, function(response) {
        if (response.success) {
            alert_float('success', 'Campaign resumed successfully');
            window.campaignsTable.ajax.reload();
        } else {
            alert_float('danger', response.message || 'Failed to resume campaign');
        }
    });
}

function viewAnalytics(id) {
    window.location.href = admin_url + 'email_marketing/analytics/campaign/' + id;
}

function executeBulkAction() {
    const action = $('#bulk-action-select').val();
    const selectedIds = $('.campaign-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    if (selectedIds.length === 0) {
        alert('Please select at least one campaign');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedIds.length} campaign(s)?`)) {
        $.post(admin_url + 'email_marketing/campaigns/bulk_action', {
            action: action,
            ids: selectedIds
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#bulk-actions-modal').modal('hide');
                window.campaignsTable.ajax.reload();
                updateBulkActionsButton();
            } else {
                alert_float('danger', response.message || 'Failed to execute bulk action');
            }
        });
    }
}
</script>

