<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Hooks
 * Integrates with Perfex CRM hooks to trigger automatic responses
 */

/**
 * Hook for when a new lead is created
 */
hooks()->add_action('lead_created', 'email_marketing_lead_created_hook');

function email_marketing_lead_created_hook($lead_id)
{
    $CI = &get_instance();
    
    // Load the lead response model
    $CI->load->model('email_marketing/lead_response_model');
    
    // Process automatic responses for new lead
    $CI->lead_response_model->process_new_lead($lead_id);
}

/**
 * Hook for when a lead status is changed
 */
hooks()->add_action('lead_status_changed', 'email_marketing_lead_status_changed_hook');

function email_marketing_lead_status_changed_hook($data)
{
    $CI = &get_instance();
    
    // Load the lead response model
    $CI->load->model('email_marketing/lead_response_model');
    
    // Extract lead ID and status information
    $lead_id = $data['lead_id'];
    $old_status = $data['old_status'];
    $new_status = $data['new_status'];
    
    // Process automatic responses for status change
    $CI->lead_response_model->process_lead_status_change($lead_id, $old_status, $new_status);
}

/**
 * Hook for when a new customer is created
 */
hooks()->add_action('customer_created', 'email_marketing_customer_created_hook');

function email_marketing_customer_created_hook($customer_id)
{
    $CI = &get_instance();
    
    // Load the automation model
    $CI->load->model('email_marketing/automation_model');
    
    // Process automation triggers for new customer
    $CI->automation_model->process_trigger('customer_created', $customer_id);
}

/**
 * Hook for email tracking (open tracking)
 */
hooks()->add_action('init', 'email_marketing_tracking_init');

function email_marketing_tracking_init()
{
    $CI = &get_instance();
    
    // Check for tracking parameters
    $tracking_token = $CI->input->get('em_track');
    $click_token = $CI->input->get('em_click');
    $unsubscribe_token = $CI->input->get('em_unsubscribe');
    
    if ($tracking_token) {
        email_marketing_track_open($tracking_token);
    }
    
    if ($click_token) {
        $url = $CI->input->get('url');
        email_marketing_track_click($click_token, $url);
    }
    
    if ($unsubscribe_token) {
        email_marketing_handle_unsubscribe($unsubscribe_token);
    }
}

/**
 * Track email open
 */
function email_marketing_track_open($tracking_token)
{
    $CI = &get_instance();
    $CI->load->model('email_marketing/analytics_model');
    
    // Log the open event
    $analytics_data = [
        'tracking_token' => $tracking_token,
        'event_type' => 'opened',
        'ip_address' => $CI->input->ip_address(),
        'user_agent' => $CI->input->user_agent(),
        'event_time' => date('Y-m-d H:i:s')
    ];
    
    $CI->analytics_model->log_event($analytics_data);
    
    // Output 1x1 transparent pixel
    header('Content-Type: image/gif');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 1x1 transparent GIF
    echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
    exit;
}

/**
 * Track email click
 */
function email_marketing_track_click($tracking_token, $url)
{
    $CI = &get_instance();
    $CI->load->model('email_marketing/analytics_model');
    
    // Log the click event
    $analytics_data = [
        'tracking_token' => $tracking_token,
        'event_type' => 'clicked',
        'event_data' => json_encode(['url' => $url]),
        'ip_address' => $CI->input->ip_address(),
        'user_agent' => $CI->input->user_agent(),
        'event_time' => date('Y-m-d H:i:s')
    ];
    
    $CI->analytics_model->log_event($analytics_data);
    
    // Redirect to the original URL
    if ($url) {
        redirect($url);
    } else {
        show_404();
    }
}

/**
 * Handle unsubscribe request
 */
function email_marketing_handle_unsubscribe($tracking_token)
{
    $CI = &get_instance();
    $CI->load->model('email_marketing/unsubscribe_model');
    
    // Process unsubscribe
    $result = $CI->unsubscribe_model->process_unsubscribe($tracking_token);
    
    if ($result) {
        // Show unsubscribe confirmation page
        $CI->load->view('email_marketing/unsubscribe_success');
    } else {
        // Show error page
        $CI->load->view('email_marketing/unsubscribe_error');
    }
    exit;
}

/**
 * Hook for cron job processing
 */
hooks()->add_action('after_cron_run', 'email_marketing_cron_hook');

function email_marketing_cron_hook()
{
    $CI = &get_instance();
    
    // Process automation queue
    $CI->load->model('email_marketing/automation_model');
    $CI->automation_model->process_queue();
    
    // Process delayed lead responses
    $CI->load->model('email_marketing/lead_response_model');
    $CI->lead_response_model->process_delayed_queue();
    
    // Process scheduled campaigns
    $CI->load->model('email_marketing/campaigns_model');
    $CI->campaigns_model->process_scheduled_campaigns();
}

/**
 * Hook for admin menu
 */
hooks()->add_action('admin_init', 'email_marketing_admin_menu_hook');

function email_marketing_admin_menu_hook()
{
    $CI = &get_instance();
    
    if (has_permission('email_marketing', '', 'view')) {
        $CI->app_menu->add_sidebar_menu_item('email_marketing', [
            'name' => _l('email_marketing'),
            'href' => admin_url('email_marketing'),
            'icon' => 'fa fa-envelope',
            'position' => 30,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_dashboard',
            'name' => _l('email_marketing_dashboard'),
            'href' => admin_url('email_marketing'),
            'position' => 1,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_campaigns',
            'name' => _l('email_marketing_campaigns'),
            'href' => admin_url('email_marketing/campaigns'),
            'position' => 2,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_templates',
            'name' => _l('email_marketing_templates'),
            'href' => admin_url('email_marketing/templates'),
            'position' => 3,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_segments',
            'name' => _l('email_marketing_segments'),
            'href' => admin_url('email_marketing/segments'),
            'position' => 4,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_automation',
            'name' => _l('email_marketing_automation'),
            'href' => admin_url('email_marketing/automation'),
            'position' => 5,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_lead_responses',
            'name' => _l('email_marketing_lead_responses'),
            'href' => admin_url('email_marketing/lead_response'),
            'position' => 6,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_analytics',
            'name' => _l('email_marketing_analytics'),
            'href' => admin_url('email_marketing/analytics'),
            'position' => 7,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug' => 'email_marketing_settings',
            'name' => _l('email_marketing_settings'),
            'href' => admin_url('email_marketing/settings'),
            'position' => 8,
        ]);
    }
}

/**
 * Hook for lead view to show email marketing history
 */
hooks()->add_action('after_lead_view_as_customer_link', 'email_marketing_lead_view_hook');

function email_marketing_lead_view_hook($lead)
{
    $CI = &get_instance();
    
    if (has_permission('email_marketing', '', 'view')) {
        echo '<div class="panel panel-info">';
        echo '<div class="panel-heading">';
        echo '<h3 class="panel-title">' . _l('email_marketing_lead_history') . '</h3>';
        echo '</div>';
        echo '<div class="panel-body">';
        
        // Load email marketing history for this lead
        $CI->load->model('email_marketing/analytics_model');
        $history = $CI->analytics_model->get_lead_email_history($lead->id);
        
        if (!empty($history)) {
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . _l('email_marketing_campaign') . '</th>';
            echo '<th>' . _l('email_marketing_sent_date') . '</th>';
            echo '<th>' . _l('email_marketing_status') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($history as $item) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($item->campaign_name) . '</td>';
                echo '<td>' . _dt($item->sent_at) . '</td>';
                echo '<td>';
                if ($item->opened_at) {
                    echo '<span class="label label-success">' . _l('email_marketing_opened') . '</span>';
                } else {
                    echo '<span class="label label-default">' . _l('email_marketing_sent') . '</span>';
                }
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        } else {
            echo '<p>' . _l('email_marketing_no_history') . '</p>';
        }
        
        echo '</div>';
        echo '</div>';
    }
}

/**
 * Hook for customer view to show email marketing history
 */
hooks()->add_action('after_customer_admins_tab', 'email_marketing_customer_view_hook');

function email_marketing_customer_view_hook($customer)
{
    $CI = &get_instance();
    
    if (has_permission('email_marketing', '', 'view')) {
        echo '<div class="tab-pane" id="email_marketing_history">';
        echo '<h4>' . _l('email_marketing_customer_history') . '</h4>';
        
        // Load email marketing history for this customer
        $CI->load->model('email_marketing/analytics_model');
        $history = $CI->analytics_model->get_customer_email_history($customer->userid);
        
        if (!empty($history)) {
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . _l('email_marketing_campaign') . '</th>';
            echo '<th>' . _l('email_marketing_contact') . '</th>';
            echo '<th>' . _l('email_marketing_sent_date') . '</th>';
            echo '<th>' . _l('email_marketing_status') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($history as $item) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($item->campaign_name) . '</td>';
                echo '<td>' . htmlspecialchars($item->contact_name) . '</td>';
                echo '<td>' . _dt($item->sent_at) . '</td>';
                echo '<td>';
                if ($item->opened_at) {
                    echo '<span class="label label-success">' . _l('email_marketing_opened') . '</span>';
                } else {
                    echo '<span class="label label-default">' . _l('email_marketing_sent') . '</span>';
                }
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        } else {
            echo '<p>' . _l('email_marketing_no_history') . '</p>';
        }
        
        echo '</div>';
    }
}

/**
 * Hook to add email marketing tab to customer view
 */
hooks()->add_action('customer_profile_tab_links', 'email_marketing_customer_tab_link');

function email_marketing_customer_tab_link($customer)
{
    if (has_permission('email_marketing', '', 'view')) {
        echo '<li><a href="#email_marketing_history" data-toggle="tab">' . _l('email_marketing') . '</a></li>';
    }
}

/**
 * Hook for settings tab
 */
hooks()->add_action('before_settings_group_view', 'email_marketing_settings_tab');

function email_marketing_settings_tab($tab)
{
    if ($tab['slug'] == 'email_marketing') {
        $CI = &get_instance();
        $CI->load->view('admin/email_marketing/settings/email_marketing_settings');
    }
}

/**
 * Hook to add email marketing settings tab
 */
hooks()->add_filter('settings_tabs', 'email_marketing_add_settings_tab');

function email_marketing_add_settings_tab($tabs)
{
    $tabs['email_marketing'] = [
        'name' => _l('email_marketing'),
        'view' => 'email_marketing_settings',
        'position' => 50,
    ];
    
    return $tabs;
}

/**
 * Hook for dashboard widgets
 */
hooks()->add_action('admin_init', 'email_marketing_dashboard_widgets');

function email_marketing_dashboard_widgets()
{
    if (has_permission('email_marketing', '', 'view')) {
        $CI = &get_instance();
        
        // Add email marketing widget to dashboard
        $CI->app_widgets->add('email_marketing_stats', [
            'name' => _l('email_marketing_dashboard_widget'),
            'view' => 'admin/email_marketing/widgets/stats_widget',
            'position' => 20,
        ]);
    }
}

/**
 * Hook for GDPR data export
 */
hooks()->add_action('gdpr_data_export', 'email_marketing_gdpr_export');

function email_marketing_gdpr_export($data, $type, $id)
{
    $CI = &get_instance();
    $CI->load->model('email_marketing/analytics_model');
    
    if ($type == 'lead') {
        $email_data = $CI->analytics_model->get_lead_email_history($id);
        if (!empty($email_data)) {
            $data['email_marketing'] = $email_data;
        }
    } elseif ($type == 'customer') {
        $email_data = $CI->analytics_model->get_customer_email_history($id);
        if (!empty($email_data)) {
            $data['email_marketing'] = $email_data;
        }
    }
    
    return $data;
}

/**
 * Hook for GDPR data deletion
 */
hooks()->add_action('gdpr_data_delete', 'email_marketing_gdpr_delete');

function email_marketing_gdpr_delete($type, $id)
{
    $CI = &get_instance();
    $CI->load->model('email_marketing/analytics_model');
    
    if ($type == 'lead') {
        $CI->analytics_model->delete_lead_data($id);
    } elseif ($type == 'customer') {
        $CI->analytics_model->delete_customer_data($id);
    }
}

/**
 * Hook for module activation
 */
hooks()->add_action('module_email_marketing_action_links', 'email_marketing_action_links');

function email_marketing_action_links($actions, $module)
{
    $actions[] = '<a href="' . admin_url('email_marketing/settings') . '">' . _l('settings') . '</a>';
    return $actions;
}

