<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Preferences - <?php echo htmlspecialchars($company_name); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            animation: fadeIn 0.5s ease-out;
        }
        
        .success-message::before {
            content: "✓";
            background: #28a745;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
            font-size: 14px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .error-message::before {
            content: "!";
            background: #dc3545;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .email-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .email-info strong {
            color: #667eea;
            font-size: 16px;
        }
        
        .preferences-grid {
            display: grid;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .preference-item {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .preference-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .preference-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.1);
        }
        
        .preference-item:hover::before {
            transform: scaleX(1);
        }
        
        .preference-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .preference-title {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }
        
        .preference-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active::before {
            transform: translateX(26px);
        }
        
        .toggle-switch input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .footer-links {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .info-text {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
            }
            
            .header, .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .preference-item {
                padding: 15px;
            }
            
            .footer-links a {
                display: block;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Email Preferences</h1>
            <p>Customize your email experience</p>
        </div>
        
        <div class="content">
            <?php if ($updated): ?>
                <div class="success-message">
                    <div>Your email preferences have been updated successfully!</div>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="error-message">
                    <div><?php echo htmlspecialchars($error); ?></div>
                </div>
            <?php endif; ?>
            
            <div class="email-info">
                Managing preferences for: <strong><?php echo htmlspecialchars($email); ?></strong>
            </div>
            
            <div class="info-text">
                Choose which types of emails you'd like to receive from us. You can update these preferences at any time.
            </div>
            
            <form method="post" action="">
                <div class="preferences-grid">
                    <div class="preference-item">
                        <div class="preference-header">
                            <div>
                                <div class="preference-title">Marketing Emails</div>
                                <div class="preference-description">Product updates, company news, and marketing content</div>
                            </div>
                            <label class="toggle-switch <?php echo $preferences['marketing_emails'] ? 'active' : ''; ?>" onclick="toggleSwitch(this)">
                                <input type="checkbox" name="marketing_emails" value="1" <?php echo $preferences['marketing_emails'] ? 'checked' : ''; ?>>
                            </label>
                        </div>
                    </div>
                    
                    <div class="preference-item">
                        <div class="preference-header">
                            <div>
                                <div class="preference-title">Transactional Emails</div>
                                <div class="preference-description">Order confirmations, receipts, and account notifications</div>
                            </div>
                            <label class="toggle-switch <?php echo $preferences['transactional_emails'] ? 'active' : ''; ?>" onclick="toggleSwitch(this)">
                                <input type="checkbox" name="transactional_emails" value="1" <?php echo $preferences['transactional_emails'] ? 'checked' : ''; ?>>
                            </label>
                        </div>
                    </div>
                    
                    <div class="preference-item">
                        <div class="preference-header">
                            <div>
                                <div class="preference-title">Newsletter</div>
                                <div class="preference-description">Weekly or monthly newsletter with industry insights</div>
                            </div>
                            <label class="toggle-switch <?php echo $preferences['newsletter'] ? 'active' : ''; ?>" onclick="toggleSwitch(this)">
                                <input type="checkbox" name="newsletter" value="1" <?php echo $preferences['newsletter'] ? 'checked' : ''; ?>>
                            </label>
                        </div>
                    </div>
                    
                    <div class="preference-item">
                        <div class="preference-header">
                            <div>
                                <div class="preference-title">Promotions & Offers</div>
                                <div class="preference-description">Special deals, discounts, and promotional content</div>
                            </div>
                            <label class="toggle-switch <?php echo $preferences['promotions'] ? 'active' : ''; ?>" onclick="toggleSwitch(this)">
                                <input type="checkbox" name="promotions" value="1" <?php echo $preferences['promotions'] ? 'checked' : ''; ?>>
                            </label>
                        </div>
                    </div>
                    
                    <div class="preference-item">
                        <div class="preference-header">
                            <div>
                                <div class="preference-title">Product Updates</div>
                                <div class="preference-description">New features, improvements, and product announcements</div>
                            </div>
                            <label class="toggle-switch <?php echo $preferences['updates'] ? 'active' : ''; ?>" onclick="toggleSwitch(this)">
                                <input type="checkbox" name="updates" value="1" <?php echo $preferences['updates'] ? 'checked' : ''; ?>>
                            </label>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn">Save Preferences</button>
            </form>
            
            <div class="footer-links">
                <a href="<?php echo site_url('email_marketing/unsubscribe?token=' . $token); ?>">Unsubscribe from All</a>
                <a href="<?php echo site_url('email_marketing/data_export'); ?>">Download My Data</a>
                <a href="<?php echo site_url('email_marketing/privacy'); ?>">Privacy Policy</a>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSwitch(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                element.classList.add('active');
            } else {
                element.classList.remove('active');
            }
            
            // Add a subtle animation
            element.style.transform = 'scale(0.95)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 100);
        }
        
        // Initialize toggle states on page load
        document.addEventListener('DOMContentLoaded', function() {
            const toggles = document.querySelectorAll('.toggle-switch');
            toggles.forEach(toggle => {
                const checkbox = toggle.querySelector('input[type="checkbox"]');
                if (checkbox.checked) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }
            });
        });
        
        // Add smooth transitions to preference items
        document.querySelectorAll('.preference-item').forEach((item, index) => {
            item.style.animationDelay = (index * 0.1) + 's';
            item.style.animation = 'slideUp 0.6s ease-out forwards';
        });
    </script>
</body>
</html>

