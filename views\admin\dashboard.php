<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-envelope"></i> <?php echo _l('email_marketing_dashboard'); ?>
                </h3>
                <div class="panel-options">
                    <a href="<?php echo admin_url('email_marketing/campaigns/campaign'); ?>" class="btn btn-primary btn-sm">
                        <i class="fa fa-plus"></i> <?php echo _l('email_marketing_new_campaign'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-lg-3 col-md-6">
        <div class="panel panel-flat stats-card">
            <div class="panel-body">
                <div class="stats-content">
                    <div class="stats-icon bg-primary">
                        <i class="fa fa-paper-plane"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number" id="total-campaigns">0</h3>
                        <p class="stats-label"><?php echo _l('email_marketing_total_campaigns'); ?></p>
                    </div>
                </div>
                <div class="stats-progress">
                    <div class="progress">
                        <div class="progress-bar bg-primary" style="width: 0%" id="campaigns-progress"></div>
                    </div>
                    <small class="text-muted" id="campaigns-change">+0% from last month</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="panel panel-flat stats-card">
            <div class="panel-body">
                <div class="stats-content">
                    <div class="stats-icon bg-success">
                        <i class="fa fa-envelope-open"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number" id="emails-sent">0</h3>
                        <p class="stats-label"><?php echo _l('email_marketing_emails_sent'); ?></p>
                    </div>
                </div>
                <div class="stats-progress">
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 0%" id="emails-progress"></div>
                    </div>
                    <small class="text-muted" id="emails-change">+0% from last month</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="panel panel-flat stats-card">
            <div class="panel-body">
                <div class="stats-content">
                    <div class="stats-icon bg-info">
                        <i class="fa fa-eye"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number" id="open-rate">0%</h3>
                        <p class="stats-label"><?php echo _l('email_marketing_open_rate'); ?></p>
                    </div>
                </div>
                <div class="stats-progress">
                    <div class="progress">
                        <div class="progress-bar bg-info" style="width: 0%" id="open-rate-progress"></div>
                    </div>
                    <small class="text-muted" id="open-rate-change">+0% from last month</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="panel panel-flat stats-card">
            <div class="panel-body">
                <div class="stats-content">
                    <div class="stats-icon bg-warning">
                        <i class="fa fa-mouse-pointer"></i>
                    </div>
                    <div class="stats-info">
                        <h3 class="stats-number" id="click-rate">0%</h3>
                        <p class="stats-label"><?php echo _l('email_marketing_click_rate'); ?></p>
                    </div>
                </div>
                <div class="stats-progress">
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: 0%" id="click-rate-progress"></div>
                    </div>
                    <small class="text-muted" id="click-rate-change">+0% from last month</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row">
    <div class="col-md-8">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-line-chart"></i> <?php echo _l('email_marketing_performance_overview'); ?>
                </h3>
                <div class="panel-options">
                    <div class="btn-group" data-toggle="buttons">
                        <label class="btn btn-default btn-sm active">
                            <input type="radio" name="chart-period" value="7" checked> 7 Days
                        </label>
                        <label class="btn btn-default btn-sm">
                            <input type="radio" name="chart-period" value="30"> 30 Days
                        </label>
                        <label class="btn btn-default btn-sm">
                            <input type="radio" name="chart-period" value="90"> 90 Days
                        </label>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <canvas id="performance-chart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-pie-chart"></i> <?php echo _l('email_marketing_campaign_types'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <canvas id="campaign-types-chart" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and Quick Actions -->
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-clock-o"></i> <?php echo _l('email_marketing_recent_campaigns'); ?>
                </h3>
                <div class="panel-options">
                    <a href="<?php echo admin_url('email_marketing/campaigns'); ?>" class="btn btn-default btn-sm">
                        <?php echo _l('email_marketing_view_all'); ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <div class="recent-campaigns-list" id="recent-campaigns">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-cogs"></i> <?php echo _l('email_marketing_quick_actions'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <div class="quick-actions-grid">
                    <a href="<?php echo admin_url('email_marketing/campaigns/campaign'); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-primary">
                            <i class="fa fa-plus"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4><?php echo _l('email_marketing_create_campaign'); ?></h4>
                            <p><?php echo _l('email_marketing_create_campaign_desc'); ?></p>
                        </div>
                    </a>
                    
                    <a href="<?php echo admin_url('email_marketing/templates/template'); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-success">
                            <i class="fa fa-file-text"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4><?php echo _l('email_marketing_create_template'); ?></h4>
                            <p><?php echo _l('email_marketing_create_template_desc'); ?></p>
                        </div>
                    </a>
                    
                    <a href="<?php echo admin_url('email_marketing/segments/segment'); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-info">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4><?php echo _l('email_marketing_create_segment'); ?></h4>
                            <p><?php echo _l('email_marketing_create_segment_desc'); ?></p>
                        </div>
                    </a>
                    
                    <a href="<?php echo admin_url('email_marketing/automation/rule'); ?>" class="quick-action-item">
                        <div class="quick-action-icon bg-warning">
                            <i class="fa fa-magic"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4><?php echo _l('email_marketing_create_automation'); ?></h4>
                            <p><?php echo _l('email_marketing_create_automation_desc'); ?></p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Automation Status and Lead Responses -->
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-robot"></i> <?php echo _l('email_marketing_automation_status'); ?>
                </h3>
                <div class="panel-options">
                    <a href="<?php echo admin_url('email_marketing/automation'); ?>" class="btn btn-default btn-sm">
                        <?php echo _l('email_marketing_manage'); ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <div class="automation-stats" id="automation-stats">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-reply"></i> <?php echo _l('email_marketing_lead_responses'); ?>
                </h3>
                <div class="panel-options">
                    <a href="<?php echo admin_url('email_marketing/lead_response'); ?>" class="btn btn-default btn-sm">
                        <?php echo _l('email_marketing_manage'); ?>
                    </a>
                </div>
            </div>
            <div class="panel-body">
                <div class="lead-response-stats" id="lead-response-stats">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard Styles */
.stats-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-content {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 24px;
}

.stats-info h3 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #333;
}

.stats-label {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.stats-progress .progress {
    height: 4px;
    margin-bottom: 5px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.quick-action-item:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.1);
    text-decoration: none;
    color: inherit;
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 20px;
}

.quick-action-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.quick-action-content p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.recent-campaigns-list .campaign-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.campaign-item:last-child {
    border-bottom: none;
}

.campaign-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 15px;
}

.campaign-status.sent { background: #28a745; }
.campaign-status.draft { background: #6c757d; }
.campaign-status.scheduled { background: #007bff; }
.campaign-status.sending { background: #ffc107; }

.campaign-info h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.campaign-meta {
    font-size: 12px;
    color: #666;
}

.automation-stats .stat-row,
.lead-response-stats .stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: #333;
}

.stat-value {
    font-weight: 600;
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* Animation for loading stats */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stats-number {
    animation: countUp 0.6s ease-out;
}
</style>

<script>
$(document).ready(function() {
    // Load dashboard data
    loadDashboardStats();
    loadPerformanceChart();
    loadCampaignTypesChart();
    loadRecentCampaigns();
    loadAutomationStats();
    loadLeadResponseStats();
    
    // Chart period change handler
    $('input[name="chart-period"]').change(function() {
        loadPerformanceChart($(this).val());
    });
});

function loadDashboardStats() {
    $.get(admin_url + 'email_marketing/dashboard_stats', function(data) {
        // Animate counter updates
        animateCounter('#total-campaigns', data.total_campaigns);
        animateCounter('#emails-sent', data.emails_sent);
        animateCounter('#open-rate', data.open_rate + '%');
        animateCounter('#click-rate', data.click_rate + '%');
        
        // Update progress bars
        updateProgressBar('#campaigns-progress', data.campaigns_change);
        updateProgressBar('#emails-progress', data.emails_change);
        updateProgressBar('#open-rate-progress', data.open_rate);
        updateProgressBar('#click-rate-progress', data.click_rate);
        
        // Update change indicators
        $('#campaigns-change').text(data.campaigns_change_text);
        $('#emails-change').text(data.emails_change_text);
        $('#open-rate-change').text(data.open_rate_change_text);
        $('#click-rate-change').text(data.click_rate_change_text);
    });
}

function loadPerformanceChart(period = 7) {
    $.get(admin_url + 'email_marketing/performance_chart', {period: period}, function(data) {
        const ctx = document.getElementById('performance-chart').getContext('2d');
        
        if (window.performanceChart) {
            window.performanceChart.destroy();
        }
        
        window.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Emails Sent',
                    data: data.emails_sent,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Opens',
                    data: data.opens,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Clicks',
                    data: data.clicks,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    });
}

function loadCampaignTypesChart() {
    $.get(admin_url + 'email_marketing/campaign_types_chart', function(data) {
        const ctx = document.getElementById('campaign-types-chart').getContext('2d');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#6f42c1'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
}

function loadRecentCampaigns() {
    $.get(admin_url + 'email_marketing/recent_campaigns', function(data) {
        let html = '';
        
        if (data.length > 0) {
            data.forEach(function(campaign) {
                html += `
                    <div class="campaign-item">
                        <div class="campaign-status ${campaign.status}"></div>
                        <div class="campaign-info flex-grow-1">
                            <h5>${campaign.name}</h5>
                            <div class="campaign-meta">
                                ${campaign.status_text} • ${campaign.created_at}
                            </div>
                        </div>
                        <div class="campaign-stats">
                            <small class="text-muted">${campaign.recipients_count} recipients</small>
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<div class="text-center text-muted">No campaigns found</div>';
        }
        
        $('#recent-campaigns').html(html);
    });
}

function loadAutomationStats() {
    $.get(admin_url + 'email_marketing/automation/dashboard_data', function(data) {
        let html = `
            <div class="stat-row">
                <span class="stat-label">Active Rules</span>
                <span class="stat-value">${data.active_rules}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Pending Queue</span>
                <span class="stat-value">${data.queue_stats.pending_count}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Sent Today</span>
                <span class="stat-value">${data.queue_stats.sent_count}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Failed</span>
                <span class="stat-value text-danger">${data.queue_stats.failed_count}</span>
            </div>
        `;
        
        $('#automation-stats').html(html);
    });
}

function loadLeadResponseStats() {
    $.get(admin_url + 'email_marketing/lead_response/dashboard_data', function(data) {
        let html = `
            <div class="stat-row">
                <span class="stat-label">Active Rules</span>
                <span class="stat-value">${data.active_rules}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Pending Queue</span>
                <span class="stat-value">${data.queue_stats.pending_count}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Sent Today</span>
                <span class="stat-value">${data.queue_stats.sent_count}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">Failed</span>
                <span class="stat-value text-danger">${data.queue_stats.failed_count}</span>
            </div>
        `;
        
        $('#lead-response-stats').html(html);
    });
}

function animateCounter(selector, endValue) {
    const element = $(selector);
    const startValue = 0;
    const duration = 1000;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(startValue + (endValue - startValue) * progress);
        element.text(typeof endValue === 'string' && endValue.includes('%') ? currentValue + '%' : currentValue);
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.text(endValue);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

function updateProgressBar(selector, percentage) {
    $(selector).animate({
        width: Math.min(percentage, 100) + '%'
    }, 1000);
}
</script>

