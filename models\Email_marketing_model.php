<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Main Model
 */
class Email_marketing_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Get all module settings
     */
    public function get_all_settings()
    {
        $this->db->select('setting_key, setting_value, setting_type');
        $this->db->from(db_prefix() . 'email_marketing_settings');
        $query = $this->db->get();
        
        $settings = [];
        foreach ($query->result() as $setting) {
            $value = $setting->setting_value;
            
            // Convert value based on type
            switch ($setting->setting_type) {
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'integer':
                    $value = (int) $value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            $settings[$setting->setting_key] = $value;
        }
        
        return $settings;
    }
    
    /**
     * Get specific setting value
     */
    public function get_setting($key, $default = null)
    {
        $this->db->select('setting_value, setting_type');
        $this->db->from(db_prefix() . 'email_marketing_settings');
        $this->db->where('setting_key', $key);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $setting = $query->row();
            $value = $setting->setting_value;
            
            // Convert value based on type
            switch ($setting->setting_type) {
                case 'boolean':
                    return (bool) $value;
                case 'integer':
                    return (int) $value;
                case 'json':
                    return json_decode($value, true);
                default:
                    return $value;
            }
        }
        
        return $default;
    }
    
    /**
     * Update module settings
     */
    public function update_settings($settings)
    {
        foreach ($settings as $key => $value) {
            // Skip non-setting fields
            if (in_array($key, ['csrf_token_name'])) {
                continue;
            }
            
            // Determine setting type
            $type = 'string';
            if (is_bool($value)) {
                $type = 'boolean';
                $value = $value ? '1' : '0';
            } elseif (is_int($value)) {
                $type = 'integer';
            } elseif (is_array($value)) {
                $type = 'json';
                $value = json_encode($value);
            }
            
            $this->db->where('setting_key', $key);
            $exists = $this->db->get(db_prefix() . 'email_marketing_settings')->num_rows() > 0;
            
            if ($exists) {
                $this->db->where('setting_key', $key);
                $this->db->update(db_prefix() . 'email_marketing_settings', [
                    'setting_value' => $value,
                    'setting_type' => $type,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $this->db->insert(db_prefix() . 'email_marketing_settings', [
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'setting_type' => $type
                ]);
            }
        }
        
        return true;
    }
    
    /**
     * Update single setting
     */
    public function update_setting($key, $value, $type = 'string')
    {
        if ($type == 'boolean') {
            $value = $value ? '1' : '0';
        } elseif ($type == 'json') {
            $value = json_encode($value);
        }
        
        $this->db->where('setting_key', $key);
        $exists = $this->db->get(db_prefix() . 'email_marketing_settings')->num_rows() > 0;
        
        if ($exists) {
            $this->db->where('setting_key', $key);
            $this->db->update(db_prefix() . 'email_marketing_settings', [
                'setting_value' => $value,
                'setting_type' => $type,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            $this->db->insert(db_prefix() . 'email_marketing_settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'setting_type' => $type
            ]);
        }
        
        return true;
    }
    
    /**
     * Check if module is enabled
     */
    public function is_enabled()
    {
        return $this->get_setting('email_marketing_enabled', true);
    }
    
    /**
     * Get module statistics for dashboard
     */
    public function get_module_stats()
    {
        $stats = [];
        
        // Total campaigns
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $stats['total_campaigns'] = $this->db->count_all_results();
        
        // Active campaigns
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $this->db->where_in('status', ['scheduled', 'sending']);
        $stats['active_campaigns'] = $this->db->count_all_results();
        
        // Total templates
        $this->db->from(db_prefix() . 'email_marketing_templates');
        $stats['total_templates'] = $this->db->count_all_results();
        
        // Total automation rules
        $this->db->from(db_prefix() . 'email_marketing_automation_rules');
        $stats['total_automation_rules'] = $this->db->count_all_results();
        
        // Active automation rules
        $this->db->from(db_prefix() . 'email_marketing_automation_rules');
        $this->db->where('is_active', 1);
        $stats['active_automation_rules'] = $this->db->count_all_results();
        
        return $stats;
    }
    
    /**
     * Get recent activity
     */
    public function get_recent_activity($limit = 10)
    {
        $this->db->select('*');
        $this->db->from(db_prefix() . 'email_marketing_analytics');
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }
    
    /**
     * Clean up old data based on retention settings
     */
    public function cleanup_old_data()
    {
        $retention_days = $this->get_setting('email_marketing_data_retention', 365);
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$retention_days} days"));
        
        // Clean up old analytics data
        $this->db->where('created_at <', $cutoff_date);
        $this->db->delete(db_prefix() . 'email_marketing_analytics');
        
        // Clean up old automation queue entries
        $this->db->where('created_at <', $cutoff_date);
        $this->db->where_in('status', ['sent', 'failed', 'cancelled']);
        $this->db->delete(db_prefix() . 'email_marketing_automation_queue');
        
        return true;
    }
}
