<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Unsubscribe Model
 * Handles GDPR-compliant unsubscribe functionality
 */
class Unsubscribe_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_unsubscribes';
        $this->preferences_table = db_prefix() . 'email_marketing_preferences';
    }
    
    /**
     * Process unsubscribe request
     */
    public function process_unsubscribe($tracking_token, $unsubscribe_type = 'all')
    {
        // Get recipient information from tracking token
        $recipient = $this->get_recipient_by_token($tracking_token);
        
        if (!$recipient) {
            return ['success' => false, 'message' => 'Invalid unsubscribe link'];
        }
        
        // Check if already unsubscribed
        $existing = $this->get_unsubscribe_record($recipient['email'], $unsubscribe_type);
        
        if ($existing) {
            return ['success' => true, 'message' => 'You are already unsubscribed', 'already_unsubscribed' => true];
        }
        
        // Create unsubscribe record
        $unsubscribe_data = [
            'email' => $recipient['email'],
            'recipient_type' => $recipient['type'],
            'recipient_id' => $recipient['id'],
            'unsubscribe_type' => $unsubscribe_type,
            'tracking_token' => $tracking_token,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $this->get_user_agent(),
            'unsubscribed_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert($this->table, $unsubscribe_data);
        $unsubscribe_id = $this->db->insert_id();
        
        if ($unsubscribe_id) {
            // Log the unsubscribe event
            $this->log_unsubscribe_event($recipient, $unsubscribe_type, $tracking_token);
            
            // Update email preferences
            $this->update_email_preferences($recipient['email'], $unsubscribe_type);
            
            return ['success' => true, 'message' => 'Successfully unsubscribed', 'unsubscribe_id' => $unsubscribe_id];
        }
        
        return ['success' => false, 'message' => 'Failed to process unsubscribe request'];
    }
    
    /**
     * Get recipient information by tracking token
     */
    private function get_recipient_by_token($tracking_token)
    {
        // Check campaign recipients
        $this->db->select('email, recipient_type, recipient_id, "campaign" as source');
        $this->db->where('tracking_token', $tracking_token);
        $campaign_recipient = $this->db->get(db_prefix() . 'email_marketing_campaign_recipients')->row_array();
        
        if ($campaign_recipient) {
            return [
                'email' => $campaign_recipient['email'],
                'type' => $campaign_recipient['recipient_type'],
                'id' => $campaign_recipient['recipient_id'],
                'source' => 'campaign'
            ];
        }
        
        // Check automation queue
        $this->db->select('email, recipient_type, recipient_id, "automation" as source');
        $this->db->where('tracking_token', $tracking_token);
        $automation_recipient = $this->db->get(db_prefix() . 'email_marketing_automation_queue')->row_array();
        
        if ($automation_recipient) {
            return [
                'email' => $automation_recipient['email'],
                'type' => $automation_recipient['recipient_type'],
                'id' => $automation_recipient['recipient_id'],
                'source' => 'automation'
            ];
        }
        
        // Check lead response queue
        $this->db->select('recipient_email as email, "lead" as recipient_type, lead_id as recipient_id, "lead_response" as source');
        $this->db->where('tracking_token', $tracking_token);
        $lead_response = $this->db->get(db_prefix() . 'email_marketing_lead_response_queue')->row_array();
        
        if ($lead_response) {
            return [
                'email' => $lead_response['email'],
                'type' => $lead_response['recipient_type'],
                'id' => $lead_response['recipient_id'],
                'source' => 'lead_response'
            ];
        }
        
        return null;
    }
    
    /**
     * Get existing unsubscribe record
     */
    private function get_unsubscribe_record($email, $unsubscribe_type)
    {
        $this->db->where('email', $email);
        $this->db->where('unsubscribe_type', $unsubscribe_type);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Log unsubscribe event for analytics
     */
    private function log_unsubscribe_event($recipient, $unsubscribe_type, $tracking_token)
    {
        $this->load->model('email_marketing/analytics_model');
        
        $analytics_data = [
            'tracking_token' => $tracking_token,
            'event_type' => 'unsubscribed',
            'event_data' => json_encode([
                'unsubscribe_type' => $unsubscribe_type,
                'recipient_type' => $recipient['type'],
                'recipient_id' => $recipient['id']
            ]),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $this->get_user_agent(),
            'event_time' => date('Y-m-d H:i:s')
        ];
        
        $this->analytics_model->log_event($analytics_data);
    }
    
    /**
     * Update email preferences
     */
    private function update_email_preferences($email, $unsubscribe_type)
    {
        // Check if preferences record exists
        $this->db->where('email', $email);
        $existing_preferences = $this->db->get($this->preferences_table)->row();
        
        if ($existing_preferences) {
            // Update existing preferences
            $update_data = [];
            
            switch ($unsubscribe_type) {
                case 'all':
                    $update_data = [
                        'marketing_emails' => 0,
                        'transactional_emails' => 0,
                        'newsletter' => 0,
                        'promotions' => 0,
                        'updates' => 0
                    ];
                    break;
                    
                case 'marketing':
                    $update_data['marketing_emails'] = 0;
                    break;
                    
                case 'newsletter':
                    $update_data['newsletter'] = 0;
                    break;
                    
                case 'promotions':
                    $update_data['promotions'] = 0;
                    break;
                    
                case 'updates':
                    $update_data['updates'] = 0;
                    break;
            }
            
            $update_data['updated_at'] = date('Y-m-d H:i:s');
            
            $this->db->where('email', $email);
            $this->db->update($this->preferences_table, $update_data);
        } else {
            // Create new preferences record
            $preferences_data = [
                'email' => $email,
                'marketing_emails' => $unsubscribe_type == 'all' || $unsubscribe_type == 'marketing' ? 0 : 1,
                'transactional_emails' => $unsubscribe_type == 'all' ? 0 : 1,
                'newsletter' => $unsubscribe_type == 'all' || $unsubscribe_type == 'newsletter' ? 0 : 1,
                'promotions' => $unsubscribe_type == 'all' || $unsubscribe_type == 'promotions' ? 0 : 1,
                'updates' => $unsubscribe_type == 'all' || $unsubscribe_type == 'updates' ? 0 : 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->insert($this->preferences_table, $preferences_data);
        }
    }
    
    /**
     * Check if email is unsubscribed
     */
    public function is_unsubscribed($email, $email_type = 'marketing')
    {
        // Check global unsubscribe
        $this->db->where('email', $email);
        $this->db->where('unsubscribe_type', 'all');
        $global_unsubscribe = $this->db->get($this->table)->row();
        
        if ($global_unsubscribe) {
            return true;
        }
        
        // Check specific type unsubscribe
        $this->db->where('email', $email);
        $this->db->where('unsubscribe_type', $email_type);
        $type_unsubscribe = $this->db->get($this->table)->row();
        
        if ($type_unsubscribe) {
            return true;
        }
        
        // Check preferences
        $this->db->where('email', $email);
        $preferences = $this->db->get($this->preferences_table)->row();
        
        if ($preferences) {
            switch ($email_type) {
                case 'marketing':
                    return !$preferences->marketing_emails;
                case 'transactional':
                    return !$preferences->transactional_emails;
                case 'newsletter':
                    return !$preferences->newsletter;
                case 'promotions':
                    return !$preferences->promotions;
                case 'updates':
                    return !$preferences->updates;
            }
        }
        
        return false;
    }
    
    /**
     * Get email preferences
     */
    public function get_email_preferences($email)
    {
        $this->db->where('email', $email);
        $preferences = $this->db->get($this->preferences_table)->row();
        
        if ($preferences) {
            return [
                'marketing_emails' => (bool)$preferences->marketing_emails,
                'transactional_emails' => (bool)$preferences->transactional_emails,
                'newsletter' => (bool)$preferences->newsletter,
                'promotions' => (bool)$preferences->promotions,
                'updates' => (bool)$preferences->updates
            ];
        }
        
        // Default preferences (all enabled)
        return [
            'marketing_emails' => true,
            'transactional_emails' => true,
            'newsletter' => true,
            'promotions' => true,
            'updates' => true
        ];
    }
    
    /**
     * Update email preferences
     */
    public function update_preferences($email, $preferences)
    {
        // Check if preferences record exists
        $this->db->where('email', $email);
        $existing = $this->db->get($this->preferences_table)->row();
        
        $preferences_data = [
            'marketing_emails' => isset($preferences['marketing_emails']) ? (int)$preferences['marketing_emails'] : 1,
            'transactional_emails' => isset($preferences['transactional_emails']) ? (int)$preferences['transactional_emails'] : 1,
            'newsletter' => isset($preferences['newsletter']) ? (int)$preferences['newsletter'] : 1,
            'promotions' => isset($preferences['promotions']) ? (int)$preferences['promotions'] : 1,
            'updates' => isset($preferences['updates']) ? (int)$preferences['updates'] : 1,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($existing) {
            $this->db->where('email', $email);
            return $this->db->update($this->preferences_table, $preferences_data);
        } else {
            $preferences_data['email'] = $email;
            $preferences_data['created_at'] = date('Y-m-d H:i:s');
            $this->db->insert($this->preferences_table, $preferences_data);
            return $this->db->insert_id();
        }
    }
    
    /**
     * Resubscribe email
     */
    public function resubscribe($email, $email_type = 'all')
    {
        if ($email_type == 'all') {
            // Remove all unsubscribe records
            $this->db->where('email', $email);
            $this->db->delete($this->table);
            
            // Update preferences to enable all
            $this->update_preferences($email, [
                'marketing_emails' => 1,
                'transactional_emails' => 1,
                'newsletter' => 1,
                'promotions' => 1,
                'updates' => 1
            ]);
        } else {
            // Remove specific type unsubscribe
            $this->db->where('email', $email);
            $this->db->where('unsubscribe_type', $email_type);
            $this->db->delete($this->table);
            
            // Update specific preference
            $preferences = $this->get_email_preferences($email);
            $preferences[$email_type] = true;
            $this->update_preferences($email, $preferences);
        }
        
        return true;
    }
    
    /**
     * Get unsubscribe statistics
     */
    public function get_unsubscribe_stats($date_from = null, $date_to = null)
    {
        if ($date_from) {
            $this->db->where('unsubscribed_at >=', $date_from);
        }
        
        if ($date_to) {
            $this->db->where('unsubscribed_at <=', $date_to);
        }
        
        $this->db->select('
            COUNT(*) as total_unsubscribes,
            unsubscribe_type,
            DATE(unsubscribed_at) as date
        ');
        $this->db->group_by('unsubscribe_type, DATE(unsubscribed_at)');
        $this->db->order_by('date', 'DESC');
        
        return $this->db->get($this->table)->result_array();
    }
    
    /**
     * Get unsubscribe list
     */
    public function get_unsubscribe_list($where = [], $limit = null, $offset = null)
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        
        $this->db->order_by('unsubscribed_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Export unsubscribe data for GDPR compliance
     */
    public function export_user_data($email)
    {
        $data = [];
        
        // Get unsubscribe records
        $this->db->where('email', $email);
        $unsubscribes = $this->db->get($this->table)->result_array();
        
        if (!empty($unsubscribes)) {
            $data['unsubscribes'] = $unsubscribes;
        }
        
        // Get preferences
        $preferences = $this->get_email_preferences($email);
        $data['preferences'] = $preferences;
        
        return $data;
    }
    
    /**
     * Delete user data for GDPR compliance
     */
    public function delete_user_data($email)
    {
        // Delete unsubscribe records
        $this->db->where('email', $email);
        $this->db->delete($this->table);
        
        // Delete preferences
        $this->db->where('email', $email);
        $this->db->delete($this->preferences_table);
        
        return true;
    }
    
    /**
     * Generate unsubscribe link
     */
    public function generate_unsubscribe_link($tracking_token, $unsubscribe_type = 'all')
    {
        return site_url('email_marketing/unsubscribe?token=' . $tracking_token . '&type=' . $unsubscribe_type);
    }
    
    /**
     * Generate preferences link
     */
    public function generate_preferences_link($tracking_token)
    {
        return site_url('email_marketing/preferences?token=' . $tracking_token);
    }
    
    /**
     * Validate unsubscribe token
     */
    public function validate_token($tracking_token)
    {
        $recipient = $this->get_recipient_by_token($tracking_token);
        return $recipient !== null;
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip()
    {
        $CI = &get_instance();
        return $CI->input->ip_address();
    }
    
    /**
     * Get user agent
     */
    private function get_user_agent()
    {
        $CI = &get_instance();
        return $CI->input->user_agent();
    }
    
    /**
     * Clean up old unsubscribe records
     */
    public function cleanup_old_records($days = 365)
    {
        $cutoff_date = date('Y-m-d H:i:s', strtotime('-' . $days . ' days'));
        
        $this->db->where('unsubscribed_at <', $cutoff_date);
        return $this->db->delete($this->table);
    }
    
    /**
     * Get suppression list for email sending
     */
    public function get_suppression_list($email_type = 'marketing')
    {
        $suppressed_emails = [];
        
        // Get globally unsubscribed emails
        $this->db->select('email');
        $this->db->where('unsubscribe_type', 'all');
        $global_unsubscribes = $this->db->get($this->table)->result_array();
        
        foreach ($global_unsubscribes as $unsubscribe) {
            $suppressed_emails[] = $unsubscribe['email'];
        }
        
        // Get type-specific unsubscribed emails
        $this->db->select('email');
        $this->db->where('unsubscribe_type', $email_type);
        $type_unsubscribes = $this->db->get($this->table)->result_array();
        
        foreach ($type_unsubscribes as $unsubscribe) {
            $suppressed_emails[] = $unsubscribe['email'];
        }
        
        // Get emails with disabled preferences
        $preference_field = $email_type . '_emails';
        if (in_array($preference_field, ['marketing_emails', 'transactional_emails', 'newsletter', 'promotions', 'updates'])) {
            $this->db->select('email');
            $this->db->where($preference_field, 0);
            $preference_unsubscribes = $this->db->get($this->preferences_table)->result_array();
            
            foreach ($preference_unsubscribes as $unsubscribe) {
                $suppressed_emails[] = $unsubscribe['email'];
            }
        }
        
        return array_unique($suppressed_emails);
    }
    
    /**
     * Bulk import suppression list
     */
    public function import_suppression_list($emails, $unsubscribe_type = 'marketing')
    {
        $imported = 0;
        
        foreach ($emails as $email) {
            $email = trim(strtolower($email));
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                continue;
            }
            
            // Check if already unsubscribed
            if ($this->is_unsubscribed($email, $unsubscribe_type)) {
                continue;
            }
            
            // Create unsubscribe record
            $unsubscribe_data = [
                'email' => $email,
                'recipient_type' => 'imported',
                'recipient_id' => 0,
                'unsubscribe_type' => $unsubscribe_type,
                'tracking_token' => '',
                'ip_address' => $this->get_client_ip(),
                'user_agent' => 'Bulk Import',
                'unsubscribed_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->insert($this->table, $unsubscribe_data);
            
            if ($this->db->insert_id()) {
                $this->update_email_preferences($email, $unsubscribe_type);
                $imported++;
            }
        }
        
        return $imported;
    }
}

