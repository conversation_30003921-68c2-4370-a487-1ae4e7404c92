<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Campaigns Model
 */
class Campaigns_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_campaigns';
    }
    
    /**
     * Get all campaigns
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single campaign
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Add new campaign
     */
    public function add($data)
    {
        $data['created_by'] = get_staff_user_id();
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Process settings if provided
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            // Add recipients if segment or conditions are provided
            if (isset($data['segment_id']) || isset($data['custom_conditions'])) {
                $this->add_campaign_recipients($insert_id, $data);
            }
            
            log_activity('New Email Marketing Campaign Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update campaign
     */
    public function update($data, $id)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Process settings if provided
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            // Update recipients if segment or conditions changed
            if (isset($data['segment_id']) || isset($data['custom_conditions'])) {
                $this->update_campaign_recipients($id, $data);
            }
            
            log_activity('Email Marketing Campaign Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete campaign
     */
    public function delete($id)
    {
        $campaign = $this->get($id);
        if (!$campaign) {
            return false;
        }
        
        // Delete related data
        $this->db->where('campaign_id', $id);
        $this->db->delete(db_prefix() . 'email_marketing_campaign_recipients');
        
        $this->db->where('campaign_id', $id);
        $this->db->delete(db_prefix() . 'email_marketing_analytics');
        
        // Delete campaign
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('Email Marketing Campaign Deleted [ID: ' . $id . ', Name: ' . $campaign->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Add recipients to campaign
     */
    private function add_campaign_recipients($campaign_id, $data)
    {
        $recipients = [];
        
        if (isset($data['segment_id']) && $data['segment_id']) {
            $this->load->model('email_marketing/segments_model');
            $recipients = $this->segments_model->get_segment_recipients($data['segment_id']);
        } elseif (isset($data['custom_conditions'])) {
            $recipients = $this->get_recipients_by_conditions($data['custom_conditions']);
        }
        
        foreach ($recipients as $recipient) {
            $recipient_data = [
                'campaign_id' => $campaign_id,
                'recipient_type' => $recipient['type'],
                'recipient_id' => $recipient['id'],
                'email' => $recipient['email'],
                'phone' => isset($recipient['phone']) ? $recipient['phone'] : null,
                'tracking_token' => $this->generate_tracking_token()
            ];
            
            $this->db->insert(db_prefix() . 'email_marketing_campaign_recipients', $recipient_data);
        }
    }
    
    /**
     * Update campaign recipients
     */
    private function update_campaign_recipients($campaign_id, $data)
    {
        // Delete existing recipients
        $this->db->where('campaign_id', $campaign_id);
        $this->db->delete(db_prefix() . 'email_marketing_campaign_recipients');
        
        // Add new recipients
        $this->add_campaign_recipients($campaign_id, $data);
    }
    
    /**
     * Get recipients by conditions
     */
    public function get_recipients_by_conditions($conditions)
    {
        $recipients = [];
        
        if (is_string($conditions)) {
            $conditions = json_decode($conditions, true);
        }
        
        // Process leads
        if (isset($conditions['include_leads']) && $conditions['include_leads']) {
            $this->db->select('id, email, phonenumber as phone, CONCAT(name, " ", lastname) as name, "lead" as type');
            $this->db->from(db_prefix() . 'leads');
            $this->db->where('email !=', '');
            
            // Apply lead filters
            if (isset($conditions['lead_sources']) && !empty($conditions['lead_sources'])) {
                $this->db->where_in('source', $conditions['lead_sources']);
            }
            
            if (isset($conditions['lead_statuses']) && !empty($conditions['lead_statuses'])) {
                $this->db->where_in('status', $conditions['lead_statuses']);
            }
            
            if (isset($conditions['assigned_staff']) && !empty($conditions['assigned_staff'])) {
                $this->db->where_in('assigned', $conditions['assigned_staff']);
            }
            
            if (isset($conditions['countries']) && !empty($conditions['countries'])) {
                $this->db->where_in('country', $conditions['countries']);
            }
            
            $leads = $this->db->get()->result_array();
            $recipients = array_merge($recipients, $leads);
        }
        
        // Process customers
        if (isset($conditions['include_customers']) && $conditions['include_customers']) {
            $this->db->select('userid as id, email, phonenumber as phone, company as name, "customer" as type');
            $this->db->from(db_prefix() . 'clients');
            $this->db->where('email !=', '');
            
            // Apply customer filters
            if (isset($conditions['customer_groups']) && !empty($conditions['customer_groups'])) {
                $this->db->where_in('groups_in', $conditions['customer_groups']);
            }
            
            if (isset($conditions['countries']) && !empty($conditions['countries'])) {
                $this->db->where_in('country', $conditions['countries']);
            }
            
            $customers = $this->db->get()->result_array();
            $recipients = array_merge($recipients, $customers);
        }
        
        // Remove duplicates based on email
        $unique_recipients = [];
        $emails = [];
        
        foreach ($recipients as $recipient) {
            if (!in_array($recipient['email'], $emails)) {
                $emails[] = $recipient['email'];
                $unique_recipients[] = $recipient;
            }
        }
        
        return $unique_recipients;
    }
    
    /**
     * Send campaign
     */
    public function send_campaign($id)
    {
        $campaign = $this->get($id);
        if (!$campaign || $campaign->status == 'sent') {
            return false;
        }
        
        // Update campaign status
        $this->db->where('id', $id);
        $this->db->update($this->table, [
            'status' => 'sending',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // Load email sending library
        $this->load->library('email_marketing/email_sender');
        
        // Get campaign recipients
        $recipients = $this->get_campaign_recipients($id);
        
        $sent_count = 0;
        $failed_count = 0;
        
        foreach ($recipients as $recipient) {
            if ($recipient->status == 'pending') {
                $result = $this->email_sender->send_campaign_email($campaign, $recipient);
                
                if ($result) {
                    $this->update_recipient_status($recipient->id, 'sent');
                    $sent_count++;
                } else {
                    $this->update_recipient_status($recipient->id, 'failed', $this->email_sender->get_last_error());
                    $failed_count++;
                }
            }
        }
        
        // Update campaign final status
        $final_status = ($failed_count == 0) ? 'sent' : 'sent';
        $this->db->where('id', $id);
        $this->db->update($this->table, [
            'status' => $final_status,
            'sent_count' => $sent_count,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        log_activity('Email Marketing Campaign Sent [ID: ' . $id . ', Sent: ' . $sent_count . ', Failed: ' . $failed_count . ']');
        
        return true;
    }
    
    /**
     * Schedule campaign
     */
    public function schedule_campaign($id, $send_date)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, [
            'status' => 'scheduled',
            'send_date' => $send_date,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Send test email
     */
    public function send_test_email($id, $test_email)
    {
        $campaign = $this->get($id);
        if (!$campaign) {
            return false;
        }
        
        $this->load->library('email_marketing/email_sender');
        
        // Create a test recipient object
        $test_recipient = (object) [
            'email' => $test_email,
            'name' => 'Test User',
            'tracking_token' => 'test_' . uniqid()
        ];
        
        return $this->email_sender->send_campaign_email($campaign, $test_recipient, true);
    }
    
    /**
     * Pause campaign
     */
    public function pause_campaign($id)
    {
        $this->db->where('id', $id);
        $this->db->where_in('status', ['sending', 'scheduled']);
        return $this->db->update($this->table, [
            'status' => 'paused',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Resume campaign
     */
    public function resume_campaign($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status', 'paused');
        return $this->db->update($this->table, [
            'status' => 'scheduled',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Cancel campaign
     */
    public function cancel_campaign($id)
    {
        $this->db->where('id', $id);
        $this->db->where_in('status', ['scheduled', 'sending', 'paused']);
        return $this->db->update($this->table, [
            'status' => 'cancelled',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Duplicate campaign
     */
    public function duplicate($id)
    {
        $campaign = $this->get($id);
        if (!$campaign) {
            return false;
        }
        
        $new_data = [
            'name' => $campaign->name . ' (Copy)',
            'subject' => $campaign->subject,
            'content' => $campaign->content,
            'template_id' => $campaign->template_id,
            'campaign_type' => $campaign->campaign_type,
            'status' => 'draft',
            'settings' => $campaign->settings
        ];
        
        return $this->add($new_data);
    }
    
    /**
     * Get campaign recipients
     */
    public function get_campaign_recipients($campaign_id)
    {
        $this->db->where('campaign_id', $campaign_id);
        return $this->db->get(db_prefix() . 'email_marketing_campaign_recipients')->result();
    }
    
    /**
     * Update recipient status
     */
    public function update_recipient_status($recipient_id, $status, $error_message = null)
    {
        $update_data = [
            'status' => $status,
            $status . '_at' => date('Y-m-d H:i:s')
        ];
        
        if ($error_message) {
            $update_data['error_message'] = $error_message;
        }
        
        $this->db->where('id', $recipient_id);
        return $this->db->update(db_prefix() . 'email_marketing_campaign_recipients', $update_data);
    }
    
    /**
     * Generate tracking token
     */
    private function generate_tracking_token()
    {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * Get campaign statistics
     */
    public function get_campaign_stats($id)
    {
        $this->db->select('
            COUNT(*) as total_recipients,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered_count,
            SUM(CASE WHEN status = "opened" THEN 1 ELSE 0 END) as opened_count,
            SUM(CASE WHEN status = "clicked" THEN 1 ELSE 0 END) as clicked_count,
            SUM(CASE WHEN status = "bounced" THEN 1 ELSE 0 END) as bounced_count,
            SUM(CASE WHEN status = "unsubscribed" THEN 1 ELSE 0 END) as unsubscribed_count
        ');
        $this->db->where('campaign_id', $id);
        $stats = $this->db->get(db_prefix() . 'email_marketing_campaign_recipients')->row_array();
        
        // Calculate rates
        if ($stats['sent_count'] > 0) {
            $stats['open_rate'] = round(($stats['opened_count'] / $stats['sent_count']) * 100, 2);
            $stats['click_rate'] = round(($stats['clicked_count'] / $stats['sent_count']) * 100, 2);
            $stats['bounce_rate'] = round(($stats['bounced_count'] / $stats['sent_count']) * 100, 2);
            $stats['unsubscribe_rate'] = round(($stats['unsubscribed_count'] / $stats['sent_count']) * 100, 2);
        } else {
            $stats['open_rate'] = 0;
            $stats['click_rate'] = 0;
            $stats['bounce_rate'] = 0;
            $stats['unsubscribe_rate'] = 0;
        }
        
        return $stats;
    }
    
    /**
     * Get total campaigns count
     */
    public function get_total_campaigns()
    {
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Get active campaigns count
     */
    public function get_active_campaigns_count()
    {
        $this->db->where_in('status', ['sending', 'scheduled']);
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Get recent campaigns
     */
    public function get_recent_campaigns($limit = 5)
    {
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Validate campaign before sending
     */
    public function validate_campaign($id)
    {
        $campaign = $this->get($id);
        $errors = [];
        
        if (!$campaign) {
            $errors[] = 'Campaign not found';
            return ['valid' => false, 'errors' => $errors];
        }
        
        if (empty($campaign->name)) {
            $errors[] = 'Campaign name is required';
        }
        
        if (empty($campaign->subject)) {
            $errors[] = 'Email subject is required';
        }
        
        if (empty($campaign->content)) {
            $errors[] = 'Email content is required';
        }
        
        // Check if campaign has recipients
        $recipients_count = $this->db->where('campaign_id', $id)->count_all_results(db_prefix() . 'email_marketing_campaign_recipients');
        if ($recipients_count == 0) {
            $errors[] = 'No recipients found for this campaign';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'recipients_count' => $recipients_count
        ];
    }
    
    /**
     * Generate campaign preview
     */
    public function generate_preview($id)
    {
        $campaign = $this->get($id);
        if (!$campaign) {
            return '';
        }
        
        $this->load->library('email_marketing/template_parser');
        
        // Sample data for preview
        $sample_data = [
            'contact_firstname' => 'John',
            'contact_lastname' => 'Doe',
            'contact_email' => '<EMAIL>',
            'company_name' => get_option('companyname'),
            'unsubscribe_url' => '#unsubscribe',
            'tracking_pixel' => ''
        ];
        
        return $this->template_parser->parse($campaign->content, $sample_data);
    }
    
    /**
     * Process scheduled campaigns
     */
    public function process_sending_queue()
    {
        // Get scheduled campaigns that are ready to send
        $this->db->where('status', 'scheduled');
        $this->db->where('send_date <=', date('Y-m-d H:i:s'));
        $campaigns = $this->db->get($this->table)->result();
        
        $processed = 0;
        
        foreach ($campaigns as $campaign) {
            $this->send_campaign($campaign->id);
            $processed++;
        }
        
        return $processed;
    }
    
    /**
     * Export campaign data
     */
    public function export_campaign_data($id)
    {
        $this->db->select('
            r.email,
            r.status,
            r.sent_at,
            r.opened_at,
            r.clicked_at,
            r.bounced_at,
            r.unsubscribed_at,
            r.error_message
        ');
        $this->db->from(db_prefix() . 'email_marketing_campaign_recipients r');
        $this->db->where('r.campaign_id', $id);
        $this->db->order_by('r.email');
        
        return $this->db->get()->result_array();
    }
    
    /**
     * Clone campaign with new name
     */
    public function clone_campaign($id, $new_name)
    {
        $campaign = $this->get($id);
        if (!$campaign) {
            return false;
        }
        
        $new_data = [
            'name' => $new_name,
            'subject' => $campaign->subject,
            'content' => $campaign->content,
            'template_id' => $campaign->template_id,
            'campaign_type' => $campaign->campaign_type,
            'status' => 'draft',
            'settings' => $campaign->settings
        ];
        
        return $this->add($new_data);
    }
}

