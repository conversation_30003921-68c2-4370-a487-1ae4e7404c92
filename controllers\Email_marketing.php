<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Main Controller
 */
class Email_marketing extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        // Load required models
        $this->load->model('email_marketing_model');
        $this->load->model('campaigns_model');
        $this->load->model('templates_model');
        $this->load->model('analytics_model');
        
        // Load language
        $this->load_module_language();
        
        // Check permissions
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
    }

    /**
     * Load module language file
     */
    private function load_module_language()
    {
        $language = $this->config->item('language');
        if (empty($language)) {
            $language = 'english';
        }

        $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/' . $language . '/email_marketing_lang.php';

        if (file_exists($lang_file)) {
            $this->lang->load('email_marketing_lang', $language, FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
        } else {
            // Fallback to english
            $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/english/email_marketing_lang.php';
            if (file_exists($lang_file)) {
                $this->lang->load('email_marketing_lang', 'english', FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
            }
        }
    }
    
    /**
     * Dashboard - Main overview page
     */
    public function index()
    {
        // Get dashboard statistics
        $data['total_campaigns'] = $this->campaigns_model->get_total_campaigns();
        $data['active_campaigns'] = $this->campaigns_model->get_active_campaigns_count();
        $data['total_emails_sent'] = $this->analytics_model->get_total_emails_sent();
        $data['average_open_rate'] = $this->analytics_model->get_average_open_rate();
        $data['average_click_rate'] = $this->analytics_model->get_average_click_rate();
        
        // Get recent campaigns
        $data['recent_campaigns'] = $this->campaigns_model->get_recent_campaigns(5);
        
        // Get automation rules count
        $this->load->model('automation_model');
        $data['automation_rules_count'] = $this->automation_model->get_total_rules();
        
        // Get chart data for the last 30 days
        $data['chart_data'] = $this->analytics_model->get_dashboard_chart_data();
        
        $data['title'] = _l('email_marketing_dashboard');
        $this->load->view('email_marketing/admin/dashboard', $data);
    }
    
    /**
     * Campaigns listing and management
     */
    public function campaigns()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/campaigns/table'));
        }
        
        $data['title'] = _l('email_marketing_campaigns');
        $this->load->view('email_marketing/admin/campaigns/manage', $data);
    }
    
    /**
     * Create new campaign
     */
    public function campaign($id = '')
    {
        if ($this->input->post()) {
            if ($id == '') {
                // Create new campaign
                if (!has_permission('email_marketing', '', 'create')) {
                    access_denied('email_marketing');
                }
                
                $campaign_id = $this->campaigns_model->add($this->input->post());
                
                if ($campaign_id) {
                    set_alert('success', _l('email_marketing_campaign_created'));
                    redirect(admin_url('email_marketing/campaign/' . $campaign_id));
                } else {
                    set_alert('danger', _l('email_marketing_error_campaign_not_found'));
                }
            } else {
                // Update existing campaign
                if (!has_permission('email_marketing', '', 'edit')) {
                    access_denied('email_marketing');
                }
                
                $success = $this->campaigns_model->update($this->input->post(), $id);
                
                if ($success) {
                    set_alert('success', _l('email_marketing_campaign_updated'));
                } else {
                    set_alert('danger', _l('email_marketing_error_campaign_not_found'));
                }
                
                redirect(admin_url('email_marketing/campaign/' . $id));
            }
        }
        
        if ($id == '') {
            $title = _l('email_marketing_new_campaign');
            $campaign = null;
        } else {
            $campaign = $this->campaigns_model->get($id);
            if (!$campaign) {
                show_404();
            }
            $title = _l('email_marketing_edit_campaign');
        }
        
        // Get available templates
        $data['templates'] = $this->templates_model->get_all(['template_type' => 'campaign']);
        
        // Get available segments
        $this->load->model('segments_model');
        $data['segments'] = $this->segments_model->get_all();
        
        $data['campaign'] = $campaign;
        $data['title'] = $title;
        $this->load->view('email_marketing/admin/campaigns/campaign', $data);
    }
    
    /**
     * Delete campaign
     */
    public function delete_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        if (!$id) {
            redirect(admin_url('email_marketing/campaigns'));
        }
        
        $response = $this->campaigns_model->delete($id);
        
        if ($response == true) {
            set_alert('success', _l('email_marketing_campaign_deleted'));
        } else {
            set_alert('warning', _l('email_marketing_error_campaign_not_found'));
        }
        
        redirect(admin_url('email_marketing/campaigns'));
    }
    
    /**
     * Send campaign
     */
    public function send_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $campaign = $this->campaigns_model->get($id);
        if (!$campaign) {
            show_404();
        }
        
        if ($campaign->status != 'draft' && $campaign->status != 'scheduled') {
            set_alert('warning', 'Campaign cannot be sent in current status.');
            redirect(admin_url('email_marketing/campaign/' . $id));
        }
        
        // Send campaign immediately
        $result = $this->campaigns_model->send_campaign($id);
        
        if ($result) {
            set_alert('success', _l('email_marketing_campaign_sent'));
        } else {
            set_alert('danger', 'Failed to send campaign.');
        }
        
        redirect(admin_url('email_marketing/campaign/' . $id));
    }
    
    /**
     * Schedule campaign
     */
    public function schedule_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        if ($this->input->post()) {
            $send_date = $this->input->post('send_date');
            $send_time = $this->input->post('send_time');
            
            $scheduled_datetime = $send_date . ' ' . $send_time;
            
            $result = $this->campaigns_model->schedule_campaign($id, $scheduled_datetime);
            
            if ($result) {
                set_alert('success', _l('email_marketing_campaign_scheduled'));
            } else {
                set_alert('danger', 'Failed to schedule campaign.');
            }
        }
        
        redirect(admin_url('email_marketing/campaign/' . $id));
    }
    
    /**
     * Test campaign
     */
    public function test_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        if ($this->input->post()) {
            $test_email = $this->input->post('test_email');
            
            $result = $this->campaigns_model->send_test_email($id, $test_email);
            
            if ($result) {
                set_alert('success', 'Test email sent successfully.');
            } else {
                set_alert('danger', 'Failed to send test email.');
            }
        }
        
        redirect(admin_url('email_marketing/campaign/' . $id));
    }
    
    /**
     * Pause campaign
     */
    public function pause_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $result = $this->campaigns_model->pause_campaign($id);
        
        if ($result) {
            set_alert('success', _l('email_marketing_campaign_paused'));
        } else {
            set_alert('danger', 'Failed to pause campaign.');
        }
        
        redirect(admin_url('email_marketing/campaigns'));
    }
    
    /**
     * Resume campaign
     */
    public function resume_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $result = $this->campaigns_model->resume_campaign($id);
        
        if ($result) {
            set_alert('success', _l('email_marketing_campaign_resumed'));
        } else {
            set_alert('danger', 'Failed to resume campaign.');
        }
        
        redirect(admin_url('email_marketing/campaigns'));
    }
    
    /**
     * Cancel campaign
     */
    public function cancel_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $result = $this->campaigns_model->cancel_campaign($id);
        
        if ($result) {
            set_alert('success', _l('email_marketing_campaign_cancelled'));
        } else {
            set_alert('danger', 'Failed to cancel campaign.');
        }
        
        redirect(admin_url('email_marketing/campaigns'));
    }
    
    /**
     * Duplicate campaign
     */
    public function duplicate_campaign($id)
    {
        if (!has_permission('email_marketing', '', 'create')) {
            access_denied('email_marketing');
        }
        
        $new_campaign_id = $this->campaigns_model->duplicate($id);
        
        if ($new_campaign_id) {
            set_alert('success', 'Campaign duplicated successfully.');
            redirect(admin_url('email_marketing/campaign/' . $new_campaign_id));
        } else {
            set_alert('danger', 'Failed to duplicate campaign.');
            redirect(admin_url('email_marketing/campaigns'));
        }
    }
    
    /**
     * Preview campaign
     */
    public function preview_campaign($id)
    {
        $campaign = $this->campaigns_model->get($id);
        if (!$campaign) {
            show_404();
        }
        
        // Generate preview HTML
        $preview_html = $this->campaigns_model->generate_preview($id);
        
        echo $preview_html;
    }
    
    /**
     * Get campaign recipients
     */
    public function campaign_recipients($id)
    {
        if ($this->input->is_ajax_request()) {
            $recipients = $this->campaigns_model->get_campaign_recipients($id);
            
            header('Content-Type: application/json');
            echo json_encode($recipients);
        }
    }
    
    /**
     * Templates management
     */
    public function templates()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/templates/table'));
        }
        
        $data['title'] = _l('email_marketing_templates');
        $this->load->view('email_marketing/admin/templates/manage', $data);
    }
    
    /**
     * Settings page
     */
    public function settings()
    {
        if ($this->input->post()) {
            if (!has_permission('email_marketing', '', 'edit')) {
                access_denied('email_marketing');
            }
            
            $this->email_marketing_model->update_settings($this->input->post());
            set_alert('success', _l('email_marketing_settings_updated'));
            redirect(admin_url('email_marketing/settings'));
        }
        
        $data['settings'] = $this->email_marketing_model->get_all_settings();
        $data['title'] = _l('email_marketing_settings');
        $this->load->view('email_marketing/admin/settings/manage', $data);
    }
}

