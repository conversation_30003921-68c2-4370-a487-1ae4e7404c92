<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Lead Response Controller
 */
class Lead_response extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        $this->load->model('email_marketing/lead_response_model');
        $this->load->model('email_marketing/templates_model');
        $this->lang->load('email_marketing');
        
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
    }
    
    /**
     * Lead response rules listing
     */
    public function index()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/lead_response/table'));
        }
        
        $data['title'] = _l('email_marketing_lead_responses');
        $data['stats'] = $this->lead_response_model->get_queue_stats();
        $this->load->view('admin/email_marketing/lead_response/manage', $data);
    }
    
    /**
     * Create/Edit lead response rule
     */
    public function rule($id = '')
    {
        if ($this->input->post()) {
            if ($id == '') {
                // Create new rule
                if (!has_permission('email_marketing', '', 'create')) {
                    access_denied('email_marketing');
                }
                
                $rule_id = $this->lead_response_model->add($this->input->post());
                
                if ($rule_id) {
                    set_alert('success', _l('email_marketing_lead_response_created'));
                    redirect(admin_url('email_marketing/lead_response/rule/' . $rule_id));
                } else {
                    set_alert('danger', 'Failed to create lead response rule.');
                }
            } else {
                // Update existing rule
                if (!has_permission('email_marketing', '', 'edit')) {
                    access_denied('email_marketing');
                }
                
                $success = $this->lead_response_model->update($this->input->post(), $id);
                
                if ($success) {
                    set_alert('success', _l('email_marketing_lead_response_updated'));
                } else {
                    set_alert('danger', 'Failed to update lead response rule.');
                }
                
                redirect(admin_url('email_marketing/lead_response/rule/' . $id));
            }
        }
        
        if ($id == '') {
            $title = _l('email_marketing_new_lead_response_rule');
            $rule = null;
        } else {
            $rule = $this->lead_response_model->get($id);
            if (!$rule) {
                show_404();
            }
            $title = _l('email_marketing_edit_lead_response_rule');
        }
        
        // Get available templates
        $data['templates'] = $this->templates_model->get_by_type('lead_response');
        if (empty($data['templates'])) {
            // Fallback to general templates
            $data['templates'] = $this->templates_model->get_all();
        }
        
        // Get condition options
        $data['lead_sources'] = $this->lead_response_model->get_lead_sources();
        $data['lead_statuses'] = $this->lead_response_model->get_lead_statuses();
        $data['staff_members'] = $this->lead_response_model->get_staff_members();
        
        $data['rule'] = $rule;
        $data['title'] = $title;
        $this->load->view('admin/email_marketing/lead_response/rule', $data);
    }
    
    /**
     * Delete lead response rule
     */
    public function delete($id)
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        if (!$id) {
            redirect(admin_url('email_marketing/lead_response'));
        }
        
        $response = $this->lead_response_model->delete($id);
        
        if ($response === true) {
            set_alert('success', _l('email_marketing_lead_response_deleted'));
        } else {
            set_alert('warning', 'Failed to delete lead response rule.');
        }
        
        redirect(admin_url('email_marketing/lead_response'));
    }
    
    /**
     * Activate lead response rule
     */
    public function activate($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $success = $this->lead_response_model->activate($id);
        
        if ($success) {
            set_alert('success', _l('email_marketing_lead_response_activated'));
        } else {
            set_alert('danger', 'Failed to activate lead response rule.');
        }
        
        redirect(admin_url('email_marketing/lead_response'));
    }
    
    /**
     * Deactivate lead response rule
     */
    public function deactivate($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $success = $this->lead_response_model->deactivate($id);
        
        if ($success) {
            set_alert('success', _l('email_marketing_lead_response_deactivated'));
        } else {
            set_alert('danger', 'Failed to deactivate lead response rule.');
        }
        
        redirect(admin_url('email_marketing/lead_response'));
    }
    
    /**
     * Duplicate lead response rule
     */
    public function duplicate($id)
    {
        if (!has_permission('email_marketing', '', 'create')) {
            access_denied('email_marketing');
        }
        
        $new_rule_id = $this->lead_response_model->duplicate($id);
        
        if ($new_rule_id) {
            set_alert('success', 'Lead response rule duplicated successfully.');
            redirect(admin_url('email_marketing/lead_response/rule/' . $new_rule_id));
        } else {
            set_alert('danger', 'Failed to duplicate lead response rule.');
            redirect(admin_url('email_marketing/lead_response'));
        }
    }
    
    /**
     * Test lead response rule
     */
    public function test_rule()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $rule_id = $this->input->post('rule_id');
        $test_email = $this->input->post('test_email');
        
        $result = $this->lead_response_model->test_rule($rule_id, $test_email);
        
        header('Content-Type: application/json');
        echo json_encode($result);
    }
    
    /**
     * Get lead response statistics
     */
    public function stats($id = null)
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = $this->lead_response_model->get_response_stats($id);
        
        header('Content-Type: application/json');
        echo json_encode($stats);
    }
    
    /**
     * Process delayed response queue manually
     */
    public function process_queue()
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $processed = $this->lead_response_model->process_delayed_queue();
        
        set_alert('success', "Processed {$processed} delayed lead responses.");
        redirect(admin_url('email_marketing/lead_response'));
    }
    
    /**
     * Lead response queue management
     */
    public function queue()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/lead_response/queue_table'));
        }
        
        $data['title'] = _l('email_marketing_lead_response_queue');
        $data['stats'] = $this->lead_response_model->get_queue_stats();
        $this->load->view('admin/email_marketing/lead_response/queue', $data);
    }
    
    /**
     * Lead response logs/history
     */
    public function logs($id = null)
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/lead_response/logs_table'));
        }
        
        $data['title'] = _l('email_marketing_lead_response_logs');
        
        if ($id) {
            $rule = $this->lead_response_model->get($id);
            if ($rule) {
                $data['title'] .= ' - ' . $rule->name;
                $data['rule'] = $rule;
            }
        }
        
        $this->load->view('admin/email_marketing/lead_response/logs', $data);
    }
    
    /**
     * Export lead response data
     */
    public function export($id = null)
    {
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
        
        $stats = $this->lead_response_model->get_response_stats($id);
        
        $filename = 'lead_responses_' . ($id ? $id . '_' : '') . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Write CSV headers
        if (!empty($stats)) {
            fputcsv($output, array_keys($stats[0]));
            
            // Write data rows
            foreach ($stats as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
    }
    
    /**
     * Settings for lead response system
     */
    public function settings()
    {
        if ($this->input->post()) {
            if (!has_permission('email_marketing', '', 'edit')) {
                access_denied('email_marketing');
            }
            
            $settings = $this->input->post();
            
            foreach ($settings as $key => $value) {
                update_option('email_marketing_lead_response_' . $key, $value);
            }
            
            set_alert('success', _l('settings_updated'));
            redirect(admin_url('email_marketing/lead_response/settings'));
        }
        
        $data['title'] = _l('email_marketing_lead_response_settings');
        $this->load->view('admin/email_marketing/lead_response/settings', $data);
    }
    
    /**
     * Bulk actions for lead response rules
     */
    public function bulk_action()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $action = $this->input->post('action');
        $rule_ids = $this->input->post('rule_ids');
        
        if (empty($rule_ids) || !is_array($rule_ids)) {
            echo json_encode(['success' => false, 'message' => 'No rules selected']);
            return;
        }
        
        $success_count = 0;
        
        foreach ($rule_ids as $rule_id) {
            switch ($action) {
                case 'activate':
                    if ($this->lead_response_model->activate($rule_id)) {
                        $success_count++;
                    }
                    break;
                    
                case 'deactivate':
                    if ($this->lead_response_model->deactivate($rule_id)) {
                        $success_count++;
                    }
                    break;
                    
                case 'delete':
                    if (has_permission('email_marketing', '', 'delete')) {
                        if ($this->lead_response_model->delete($rule_id)) {
                            $success_count++;
                        }
                    }
                    break;
            }
        }
        
        $message = "Successfully processed {$success_count} out of " . count($rule_ids) . " rules.";
        echo json_encode(['success' => true, 'message' => $message]);
    }
    
    /**
     * Preview lead response rule conditions
     */
    public function preview_conditions()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $conditions = $this->input->post('conditions');
        
        // This would simulate how many leads would match the conditions
        // For now, return a sample count
        $estimated_matches = rand(10, 100);
        
        echo json_encode([
            'success' => true,
            'estimated_matches' => $estimated_matches,
            'message' => "Approximately {$estimated_matches} leads would match these conditions."
        ]);
    }
    
    /**
     * Get lead response dashboard data
     */
    public function dashboard_data()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $total_rules = $this->lead_response_model->get_total_rules();
        $active_rules = $this->lead_response_model->get_active_rules_count();
        $queue_stats = $this->lead_response_model->get_queue_stats();
        $recent_stats = $this->lead_response_model->get_response_stats();
        
        $dashboard_data = [
            'total_rules' => $total_rules,
            'active_rules' => $active_rules,
            'queue_stats' => $queue_stats,
            'recent_stats' => array_slice($recent_stats, 0, 7) // Last 7 days
        ];
        
        header('Content-Type: application/json');
        echo json_encode($dashboard_data);
    }
    
    /**
     * Clean up old lead response data
     */
    public function cleanup()
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        $days = $this->input->post('days') ?: 90;
        $deleted = $this->lead_response_model->cleanup_old_logs($days);
        
        set_alert('success', "Cleaned up {$deleted} old lead response records.");
        redirect(admin_url('email_marketing/lead_response'));
    }
    
    /**
     * Manual trigger for testing lead response
     */
    public function manual_trigger()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $lead_id = $this->input->post('lead_id');
        
        if (!$lead_id) {
            echo json_encode(['success' => false, 'message' => 'Lead ID is required']);
            return;
        }
        
        $responses_sent = $this->lead_response_model->process_new_lead($lead_id);
        
        if ($responses_sent > 0) {
            echo json_encode([
                'success' => true,
                'message' => "Sent {$responses_sent} automatic response(s) to lead."
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'No matching response rules found for this lead.'
            ]);
        }
    }
}

