<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Lead Response Model
 * Handles automatic email responses to new leads based on source and status
 */
class Lead_response_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_lead_responses';
    }
    
    /**
     * Get all lead response rules
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('priority', 'ASC');
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single lead response rule
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Add new lead response rule
     */
    public function add($data)
    {
        $data['created_by'] = get_staff_user_id();
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Process conditions if provided as array
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            log_activity('New Lead Response Rule Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update lead response rule
     */
    public function update($data, $id)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Process conditions if provided as array
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            log_activity('Lead Response Rule Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete lead response rule
     */
    public function delete($id)
    {
        $rule = $this->get($id);
        if (!$rule) {
            return false;
        }
        
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('Lead Response Rule Deleted [ID: ' . $id . ', Name: ' . $rule->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Activate lead response rule
     */
    public function activate($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_active' => 1]);
    }
    
    /**
     * Deactivate lead response rule
     */
    public function deactivate($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_active' => 0]);
    }
    
    /**
     * Process new lead for automatic responses
     */
    public function process_new_lead($lead_id)
    {
        // Get lead information
        $lead = $this->get_lead_details($lead_id);
        if (!$lead) {
            return false;
        }
        
        // Get active response rules ordered by priority
        $this->db->where('is_active', 1);
        $this->db->order_by('priority', 'ASC');
        $rules = $this->db->get($this->table)->result();
        
        $responses_sent = 0;
        
        foreach ($rules as $rule) {
            if ($this->check_lead_matches_rule($lead, $rule)) {
                if ($this->send_lead_response($lead, $rule)) {
                    $responses_sent++;
                    
                    // If rule is set to stop processing after match, break
                    if ($rule->stop_on_match) {
                        break;
                    }
                }
            }
        }
        
        return $responses_sent;
    }
    
    /**
     * Process lead status change for automatic responses
     */
    public function process_lead_status_change($lead_id, $old_status, $new_status)
    {
        // Get lead information
        $lead = $this->get_lead_details($lead_id);
        if (!$lead) {
            return false;
        }
        
        // Get active response rules for status changes
        $this->db->where('is_active', 1);
        $this->db->where('trigger_event', 'status_change');
        $this->db->order_by('priority', 'ASC');
        $rules = $this->db->get($this->table)->result();
        
        $responses_sent = 0;
        
        foreach ($rules as $rule) {
            if ($this->check_status_change_matches_rule($lead, $rule, $old_status, $new_status)) {
                if ($this->send_lead_response($lead, $rule)) {
                    $responses_sent++;
                    
                    // If rule is set to stop processing after match, break
                    if ($rule->stop_on_match) {
                        break;
                    }
                }
            }
        }
        
        return $responses_sent;
    }
    
    /**
     * Get lead details with related information
     */
    private function get_lead_details($lead_id)
    {
        $this->db->select('
            l.*,
            ls.name as source_name,
            lst.name as status_name,
            CONCAT(s.firstname, " ", s.lastname) as staff_name,
            s.email as staff_email
        ');
        $this->db->from(db_prefix() . 'leads l');
        $this->db->join(db_prefix() . 'leads_sources ls', 'ls.id = l.source', 'left');
        $this->db->join(db_prefix() . 'leads_status lst', 'lst.id = l.status', 'left');
        $this->db->join(db_prefix() . 'staff s', 's.staffid = l.assigned', 'left');
        $this->db->where('l.id', $lead_id);
        
        return $this->db->get()->row();
    }
    
    /**
     * Check if lead matches response rule conditions
     */
    private function check_lead_matches_rule($lead, $rule)
    {
        $conditions = json_decode($rule->conditions, true);
        
        if (empty($conditions)) {
            return true; // No conditions means always match
        }
        
        // Check lead source condition
        if (isset($conditions['sources']) && !empty($conditions['sources'])) {
            if (!in_array($lead->source, $conditions['sources'])) {
                return false;
            }
        }
        
        // Check lead status condition
        if (isset($conditions['statuses']) && !empty($conditions['statuses'])) {
            if (!in_array($lead->status, $conditions['statuses'])) {
                return false;
            }
        }
        
        // Check assigned staff condition
        if (isset($conditions['assigned_staff']) && !empty($conditions['assigned_staff'])) {
            if (!in_array($lead->assigned, $conditions['assigned_staff'])) {
                return false;
            }
        }
        
        // Check country condition
        if (isset($conditions['countries']) && !empty($conditions['countries'])) {
            if (!in_array($lead->country, $conditions['countries'])) {
                return false;
            }
        }
        
        // Check time-based conditions
        if (isset($conditions['time_restrictions'])) {
            if (!$this->check_time_restrictions($conditions['time_restrictions'])) {
                return false;
            }
        }
        
        // Check if lead already received this response
        if ($rule->send_once_per_lead) {
            if ($this->has_lead_received_response($lead->id, $rule->id)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check if status change matches rule conditions
     */
    private function check_status_change_matches_rule($lead, $rule, $old_status, $new_status)
    {
        $conditions = json_decode($rule->conditions, true);
        
        // Check if the status change matches the rule
        if (isset($conditions['from_status']) && !empty($conditions['from_status'])) {
            if (!in_array($old_status, $conditions['from_status'])) {
                return false;
            }
        }
        
        if (isset($conditions['to_status']) && !empty($conditions['to_status'])) {
            if (!in_array($new_status, $conditions['to_status'])) {
                return false;
            }
        }
        
        // Check other lead conditions
        return $this->check_lead_matches_rule($lead, $rule);
    }
    
    /**
     * Check time restrictions
     */
    private function check_time_restrictions($time_restrictions)
    {
        $current_time = date('H:i');
        $current_day = date('w'); // 0 = Sunday, 6 = Saturday
        
        // Check business hours
        if (isset($time_restrictions['business_hours_only']) && $time_restrictions['business_hours_only']) {
            $start_time = isset($time_restrictions['start_time']) ? $time_restrictions['start_time'] : '09:00';
            $end_time = isset($time_restrictions['end_time']) ? $time_restrictions['end_time'] : '17:00';
            
            if ($current_time < $start_time || $current_time > $end_time) {
                return false;
            }
        }
        
        // Check business days only
        if (isset($time_restrictions['business_days_only']) && $time_restrictions['business_days_only']) {
            if ($current_day == 0 || $current_day == 6) { // Sunday or Saturday
                return false;
            }
        }
        
        // Check specific days
        if (isset($time_restrictions['allowed_days']) && !empty($time_restrictions['allowed_days'])) {
            if (!in_array($current_day, $time_restrictions['allowed_days'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check if lead has already received this response
     */
    private function has_lead_received_response($lead_id, $rule_id)
    {
        $this->db->where('lead_id', $lead_id);
        $this->db->where('rule_id', $rule_id);
        $count = $this->db->count_all_results(db_prefix() . 'email_marketing_lead_response_log');
        
        return $count > 0;
    }
    
    /**
     * Send lead response email
     */
    private function send_lead_response($lead, $rule)
    {
        if (!$rule->template_id) {
            return false;
        }
        
        // Get template
        $this->load->model('email_marketing/templates_model');
        $template = $this->templates_model->get($rule->template_id);
        
        if (!$template) {
            return false;
        }
        
        // Create recipient object
        $recipient = (object) [
            'email' => $lead->email,
            'name' => $lead->name . ' ' . $lead->lastname,
            'phone' => $lead->phonenumber,
            'recipient_type' => 'lead',
            'recipient_id' => $lead->id,
            'tracking_token' => $this->generate_tracking_token()
        ];
        
        // Create campaign-like object for email sending
        $campaign = (object) [
            'id' => 'lead_response_' . $rule->id,
            'name' => $rule->name,
            'subject' => $template->subject,
            'content' => $template->content,
            'campaign_type' => 'email'
        ];
        
        // Send email with delay if specified
        if ($rule->delay_minutes > 0) {
            // Queue for later sending
            $this->queue_delayed_response($lead, $rule, $recipient, $template);
            return true;
        } else {
            // Send immediately
            $this->load->library('email_marketing/email_sender');
            $result = $this->email_sender->send_campaign_email($campaign, $recipient);
            
            if ($result) {
                $this->log_response_sent($lead->id, $rule->id, $recipient->email);
            }
            
            return $result;
        }
    }
    
    /**
     * Queue delayed response
     */
    private function queue_delayed_response($lead, $rule, $recipient, $template)
    {
        $scheduled_at = date('Y-m-d H:i:s', strtotime('+' . $rule->delay_minutes . ' minutes'));
        
        $queue_data = [
            'rule_id' => $rule->id,
            'lead_id' => $lead->id,
            'recipient_email' => $recipient->email,
            'template_id' => $template->id,
            'tracking_token' => $recipient->tracking_token,
            'scheduled_at' => $scheduled_at,
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert(db_prefix() . 'email_marketing_lead_response_queue', $queue_data);
        
        return $this->db->insert_id();
    }
    
    /**
     * Process delayed response queue
     */
    public function process_delayed_queue()
    {
        // Get pending queue items that are ready to be processed
        $this->db->where('status', 'pending');
        $this->db->where('scheduled_at <=', date('Y-m-d H:i:s'));
        $this->db->order_by('scheduled_at');
        $this->db->limit(50); // Process in batches
        $queue_items = $this->db->get(db_prefix() . 'email_marketing_lead_response_queue')->result();
        
        $processed = 0;
        
        foreach ($queue_items as $item) {
            if ($this->process_delayed_queue_item($item)) {
                $processed++;
            }
        }
        
        return $processed;
    }
    
    /**
     * Process single delayed queue item
     */
    private function process_delayed_queue_item($item)
    {
        // Mark as processing
        $this->db->where('id', $item->id);
        $this->db->update(db_prefix() . 'email_marketing_lead_response_queue', [
            'status' => 'processing',
            'processed_at' => date('Y-m-d H:i:s')
        ]);
        
        // Get rule and template
        $rule = $this->get($item->rule_id);
        $this->load->model('email_marketing/templates_model');
        $template = $this->templates_model->get($item->template_id);
        
        if (!$rule || !$template) {
            $this->mark_queue_item_failed($item->id, 'Rule or template not found');
            return false;
        }
        
        // Get lead details
        $lead = $this->get_lead_details($item->lead_id);
        if (!$lead) {
            $this->mark_queue_item_failed($item->id, 'Lead not found');
            return false;
        }
        
        // Create recipient object
        $recipient = (object) [
            'email' => $item->recipient_email,
            'name' => $lead->name . ' ' . $lead->lastname,
            'phone' => $lead->phonenumber,
            'recipient_type' => 'lead',
            'recipient_id' => $lead->id,
            'tracking_token' => $item->tracking_token
        ];
        
        // Create campaign-like object
        $campaign = (object) [
            'id' => 'lead_response_' . $rule->id,
            'name' => $rule->name,
            'subject' => $template->subject,
            'content' => $template->content,
            'campaign_type' => 'email'
        ];
        
        // Send email
        $this->load->library('email_marketing/email_sender');
        $result = $this->email_sender->send_campaign_email($campaign, $recipient);
        
        if ($result) {
            // Mark as sent
            $this->db->where('id', $item->id);
            $this->db->update(db_prefix() . 'email_marketing_lead_response_queue', ['status' => 'sent']);
            
            // Log response
            $this->log_response_sent($item->lead_id, $item->rule_id, $item->recipient_email);
        } else {
            $this->mark_queue_item_failed($item->id, $this->email_sender->get_last_error());
        }
        
        return $result;
    }
    
    /**
     * Mark queue item as failed
     */
    private function mark_queue_item_failed($item_id, $error_message)
    {
        $this->db->where('id', $item_id);
        $this->db->update(db_prefix() . 'email_marketing_lead_response_queue', [
            'status' => 'failed',
            'error_message' => $error_message
        ]);
    }
    
    /**
     * Log response sent
     */
    private function log_response_sent($lead_id, $rule_id, $email)
    {
        $log_data = [
            'lead_id' => $lead_id,
            'rule_id' => $rule_id,
            'email' => $email,
            'sent_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert(db_prefix() . 'email_marketing_lead_response_log', $log_data);
    }
    
    /**
     * Generate tracking token
     */
    private function generate_tracking_token()
    {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * Get lead response statistics
     */
    public function get_response_stats($rule_id = null)
    {
        if ($rule_id) {
            $this->db->where('rule_id', $rule_id);
        }
        
        $this->db->select('
            COUNT(*) as total_sent,
            COUNT(DISTINCT lead_id) as unique_leads,
            DATE(sent_at) as date
        ');
        $this->db->group_by('DATE(sent_at)');
        $this->db->order_by('date', 'DESC');
        $this->db->limit(30); // Last 30 days
        
        return $this->db->get(db_prefix() . 'email_marketing_lead_response_log')->result_array();
    }
    
    /**
     * Get queue statistics
     */
    public function get_queue_stats()
    {
        $this->db->select('
            COUNT(*) as total_queued,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = "processing" THEN 1 ELSE 0 END) as processing_count,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count
        ');
        
        return $this->db->get(db_prefix() . 'email_marketing_lead_response_queue')->row_array();
    }
    
    /**
     * Test lead response rule
     */
    public function test_rule($rule_id, $test_email)
    {
        $rule = $this->get($rule_id);
        if (!$rule) {
            return ['success' => false, 'message' => 'Rule not found'];
        }
        
        // Get template
        $this->load->model('email_marketing/templates_model');
        $template = $this->templates_model->get($rule->template_id);
        
        if (!$template) {
            return ['success' => false, 'message' => 'Template not found'];
        }
        
        // Create test recipient
        $recipient = (object) [
            'email' => $test_email,
            'name' => 'Test Lead',
            'phone' => '+1234567890',
            'recipient_type' => 'test',
            'recipient_id' => 0,
            'tracking_token' => 'test_' . uniqid()
        ];
        
        // Create test campaign
        $campaign = (object) [
            'id' => 'test_lead_response_' . $rule->id,
            'name' => $rule->name . ' (Test)',
            'subject' => $template->subject,
            'content' => $template->content,
            'campaign_type' => 'email'
        ];
        
        // Send test email
        $this->load->library('email_marketing/email_sender');
        $result = $this->email_sender->send_campaign_email($campaign, $recipient, true);
        
        if ($result) {
            return ['success' => true, 'message' => 'Test email sent successfully'];
        } else {
            return ['success' => false, 'message' => $this->email_sender->get_last_error()];
        }
    }
    
    /**
     * Duplicate lead response rule
     */
    public function duplicate($id)
    {
        $rule = $this->get($id);
        if (!$rule) {
            return false;
        }
        
        $new_data = [
            'name' => $rule->name . ' (Copy)',
            'description' => $rule->description,
            'trigger_event' => $rule->trigger_event,
            'conditions' => $rule->conditions,
            'template_id' => $rule->template_id,
            'delay_minutes' => $rule->delay_minutes,
            'priority' => $rule->priority + 1,
            'send_once_per_lead' => $rule->send_once_per_lead,
            'stop_on_match' => $rule->stop_on_match,
            'is_active' => 0 // Start as inactive
        ];
        
        return $this->add($new_data);
    }
    
    /**
     * Get available lead sources
     */
    public function get_lead_sources()
    {
        $this->db->select('id, name');
        $this->db->from(db_prefix() . 'leads_sources');
        $this->db->order_by('name');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available lead statuses
     */
    public function get_lead_statuses()
    {
        $this->db->select('id, name, color');
        $this->db->from(db_prefix() . 'leads_status');
        $this->db->order_by('statusorder');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available staff members
     */
    public function get_staff_members()
    {
        $this->db->select('staffid as id, CONCAT(firstname, " ", lastname) as name');
        $this->db->from(db_prefix() . 'staff');
        $this->db->where('active', 1);
        $this->db->order_by('firstname, lastname');
        return $this->db->get()->result_array();
    }
    
    /**
     * Clean up old response logs
     */
    public function cleanup_old_logs($days = 90)
    {
        $cutoff_date = date('Y-m-d H:i:s', strtotime('-' . $days . ' days'));
        
        $this->db->where('sent_at <', $cutoff_date);
        return $this->db->delete(db_prefix() . 'email_marketing_lead_response_log');
    }
}

