<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Sender Library
 * Handles email sending with SMTP integration, tracking, and delivery management
 */
class Email_sender
{
    private $CI;
    private $smtp_config;
    private $last_error;
    private $tracking_enabled;
    
    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('email_marketing/smtp_model');
        $this->CI->load->library('email_marketing/template_parser');
        $this->tracking_enabled = get_option('email_marketing_tracking_enabled');
        $this->last_error = '';
    }
    
    /**
     * Send campaign email to recipient
     */
    public function send_campaign_email($campaign, $recipient, $is_test = false)
    {
        try {
            // Get SMTP configuration
            $this->smtp_config = $this->CI->smtp_model->get_default_config();
            if (!$this->smtp_config) {
                $this->last_error = 'No SMTP configuration found';
                return false;
            }
            
            // Check rate limits
            if (!$is_test && !$this->check_rate_limits()) {
                $this->last_error = 'Rate limit exceeded';
                return false;
            }
            
            // Prepare email data
            $email_data = $this->prepare_email_data($campaign, $recipient, $is_test);
            
            // Send email
            $result = $this->send_email($email_data);
            
            if ($result && !$is_test) {
                // Update rate limit counters
                $this->update_rate_limits();
                
                // Log analytics
                $this->log_email_sent($campaign, $recipient);
            }
            
            return $result;
            
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * Send automation email
     */
    public function send_automation_email($automation_rule, $recipient, $template)
    {
        try {
            // Get SMTP configuration
            $this->smtp_config = $this->CI->smtp_model->get_default_config();
            if (!$this->smtp_config) {
                $this->last_error = 'No SMTP configuration found';
                return false;
            }
            
            // Check rate limits
            if (!$this->check_rate_limits()) {
                $this->last_error = 'Rate limit exceeded';
                return false;
            }
            
            // Create campaign-like object for template parsing
            $campaign = (object) [
                'id' => 'auto_' . $automation_rule->id,
                'name' => $automation_rule->name,
                'subject' => $template->subject,
                'content' => $template->content,
                'campaign_type' => 'email'
            ];
            
            // Prepare email data
            $email_data = $this->prepare_email_data($campaign, $recipient, false);
            
            // Send email
            $result = $this->send_email($email_data);
            
            if ($result) {
                // Update rate limit counters
                $this->update_rate_limits();
                
                // Log analytics for automation
                $this->log_automation_email_sent($automation_rule, $recipient);
            }
            
            return $result;
            
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }
    
    /**
     * Prepare email data for sending
     */
    private function prepare_email_data($campaign, $recipient, $is_test = false)
    {
        // Get recipient data for merge tags
        $recipient_data = $this->get_recipient_data($recipient);
        
        // Parse subject and content with merge tags
        $parsed_subject = $this->CI->template_parser->parse($campaign->subject, $recipient_data);
        $parsed_content = $this->CI->template_parser->parse($campaign->content, $recipient_data);
        
        // Add tracking pixel if enabled and not a test
        if ($this->tracking_enabled && !$is_test && isset($recipient->tracking_token)) {
            $tracking_pixel = $this->generate_tracking_pixel($recipient->tracking_token);
            $parsed_content = $this->add_tracking_pixel($parsed_content, $tracking_pixel);
        }
        
        // Add click tracking to links if enabled and not a test
        if ($this->tracking_enabled && !$is_test && isset($recipient->tracking_token)) {
            $parsed_content = $this->add_click_tracking($parsed_content, $recipient->tracking_token);
        }
        
        // Add unsubscribe link
        $unsubscribe_url = $this->generate_unsubscribe_url($recipient);
        $parsed_content = str_replace('{unsubscribe_url}', $unsubscribe_url, $parsed_content);
        
        return [
            'to_email' => $recipient->email,
            'to_name' => isset($recipient->name) ? $recipient->name : '',
            'subject' => $parsed_subject,
            'content' => $parsed_content,
            'campaign_id' => $campaign->id,
            'recipient_id' => isset($recipient->id) ? $recipient->id : null,
            'tracking_token' => isset($recipient->tracking_token) ? $recipient->tracking_token : null
        ];
    }
    
    /**
     * Get recipient data for merge tags
     */
    private function get_recipient_data($recipient)
    {
        $data = [
            'contact_firstname' => '',
            'contact_lastname' => '',
            'contact_email' => $recipient->email,
            'contact_phone' => isset($recipient->phone) ? $recipient->phone : '',
            'company_name' => get_option('companyname'),
            'company_address' => get_option('company_address'),
            'company_phone' => get_option('company_phonenumber'),
            'company_email' => get_option('company_email'),
            'company_website' => get_option('company_website'),
            'current_date' => date('F j, Y'),
            'current_time' => date('g:i A'),
            'month' => date('F'),
            'year' => date('Y'),
            'unsubscribe_url' => '',
            'tracking_pixel' => ''
        ];
        
        // Get specific data based on recipient type
        if (isset($recipient->recipient_type)) {
            switch ($recipient->recipient_type) {
                case 'lead':
                    $lead_data = $this->get_lead_data($recipient->recipient_id);
                    $data = array_merge($data, $lead_data);
                    break;
                    
                case 'customer':
                    $customer_data = $this->get_customer_data($recipient->recipient_id);
                    $data = array_merge($data, $customer_data);
                    break;
                    
                case 'contact':
                    $contact_data = $this->get_contact_data($recipient->recipient_id);
                    $data = array_merge($data, $contact_data);
                    break;
            }
        } else {
            // Try to extract name from recipient object
            if (isset($recipient->name)) {
                $name_parts = explode(' ', $recipient->name, 2);
                $data['contact_firstname'] = $name_parts[0];
                $data['contact_lastname'] = isset($name_parts[1]) ? $name_parts[1] : '';
            }
        }
        
        return $data;
    }
    
    /**
     * Get lead data for merge tags
     */
    private function get_lead_data($lead_id)
    {
        $this->CI->db->select('
            l.*,
            ls.name as source_name,
            lst.name as status_name,
            CONCAT(s.firstname, " ", s.lastname) as staff_name,
            s.email as staff_email
        ');
        $this->CI->db->from(db_prefix() . 'leads l');
        $this->CI->db->join(db_prefix() . 'leads_sources ls', 'ls.id = l.source', 'left');
        $this->CI->db->join(db_prefix() . 'leads_status lst', 'lst.id = l.status', 'left');
        $this->CI->db->join(db_prefix() . 'staff s', 's.staffid = l.assigned', 'left');
        $this->CI->db->where('l.id', $lead_id);
        
        $lead = $this->CI->db->get()->row();
        
        if ($lead) {
            return [
                'contact_firstname' => $lead->name,
                'contact_lastname' => $lead->lastname,
                'contact_phone' => $lead->phonenumber,
                'lead_source' => $lead->source_name,
                'lead_status' => $lead->status_name,
                'staff_firstname' => $lead->firstname ?? '',
                'staff_lastname' => $lead->lastname ?? '',
                'staff_email' => $lead->staff_email ?? ''
            ];
        }
        
        return [];
    }
    
    /**
     * Get customer data for merge tags
     */
    private function get_customer_data($customer_id)
    {
        $this->CI->db->select('c.*, cg.name as group_name');
        $this->CI->db->from(db_prefix() . 'clients c');
        $this->CI->db->join(db_prefix() . 'customers_groups cg', 'FIND_IN_SET(cg.id, c.groups_in)', 'left');
        $this->CI->db->where('c.userid', $customer_id);
        
        $customer = $this->CI->db->get()->row();
        
        if ($customer) {
            return [
                'contact_firstname' => $customer->company,
                'contact_lastname' => '',
                'contact_phone' => $customer->phonenumber,
                'customer_group' => $customer->group_name ?? ''
            ];
        }
        
        return [];
    }
    
    /**
     * Get contact data for merge tags
     */
    private function get_contact_data($contact_id)
    {
        $this->CI->db->select('co.*, c.company');
        $this->CI->db->from(db_prefix() . 'contacts co');
        $this->CI->db->join(db_prefix() . 'clients c', 'c.userid = co.userid', 'left');
        $this->CI->db->where('co.id', $contact_id);
        
        $contact = $this->CI->db->get()->row();
        
        if ($contact) {
            return [
                'contact_firstname' => $contact->firstname,
                'contact_lastname' => $contact->lastname,
                'contact_phone' => $contact->phonenumber,
                'company_name' => $contact->company ?? get_option('companyname')
            ];
        }
        
        return [];
    }
    
    /**
     * Send email using SMTP
     */
    private function send_email($email_data)
    {
        // Configure CodeIgniter email library
        $config = [
            'protocol' => 'smtp',
            'smtp_host' => $this->smtp_config->smtp_host,
            'smtp_port' => $this->smtp_config->smtp_port,
            'smtp_user' => $this->smtp_config->smtp_username,
            'smtp_pass' => $this->smtp_config->smtp_password,
            'smtp_crypto' => $this->smtp_config->smtp_encryption,
            'mailtype' => 'html',
            'charset' => 'utf-8',
            'newline' => "\r\n",
            'wordwrap' => true
        ];
        
        $this->CI->load->library('email');
        $this->CI->email->initialize($config);
        
        // Set email parameters
        $this->CI->email->from($this->smtp_config->from_email, $this->smtp_config->from_name);
        $this->CI->email->to($email_data['to_email']);
        
        if ($this->smtp_config->reply_to_email) {
            $this->CI->email->reply_to($this->smtp_config->reply_to_email);
        }
        
        $this->CI->email->subject($email_data['subject']);
        $this->CI->email->message($email_data['content']);
        
        // Add custom headers
        $this->CI->email->set_header('X-Mailer', 'Perfex CRM Email Marketing Module');
        $this->CI->email->set_header('X-Campaign-ID', $email_data['campaign_id']);
        
        if ($email_data['tracking_token']) {
            $this->CI->email->set_header('X-Tracking-Token', $email_data['tracking_token']);
        }
        
        // Send email
        $result = $this->CI->email->send();
        
        if (!$result) {
            $this->last_error = $this->CI->email->print_debugger();
        }
        
        return $result;
    }
    
    /**
     * Check rate limits
     */
    private function check_rate_limits()
    {
        if (!$this->smtp_config) {
            return false;
        }
        
        // Check daily limit
        if ($this->smtp_config->daily_limit > 0) {
            $today = date('Y-m-d');
            if ($this->smtp_config->last_reset_date != $today) {
                // Reset daily counter
                $this->CI->smtp_model->reset_daily_counter($this->smtp_config->id);
                $this->smtp_config->sent_today = 0;
            }
            
            if ($this->smtp_config->sent_today >= $this->smtp_config->daily_limit) {
                return false;
            }
        }
        
        // Check hourly limit
        if ($this->smtp_config->hourly_limit > 0) {
            $current_hour = date('Y-m-d H:00:00');
            if ($this->smtp_config->last_reset_hour != $current_hour) {
                // Reset hourly counter
                $this->CI->smtp_model->reset_hourly_counter($this->smtp_config->id);
                $this->smtp_config->sent_this_hour = 0;
            }
            
            if ($this->smtp_config->sent_this_hour >= $this->smtp_config->hourly_limit) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Update rate limit counters
     */
    private function update_rate_limits()
    {
        if ($this->smtp_config) {
            $this->CI->smtp_model->increment_counters($this->smtp_config->id);
        }
    }
    
    /**
     * Generate tracking pixel
     */
    private function generate_tracking_pixel($tracking_token)
    {
        $tracking_url = site_url('?em_track=' . $tracking_token);
        return '<img src="' . $tracking_url . '" width="1" height="1" style="display:none;" alt="" />';
    }
    
    /**
     * Add tracking pixel to email content
     */
    private function add_tracking_pixel($content, $tracking_pixel)
    {
        // Add tracking pixel before closing body tag
        if (strpos($content, '</body>') !== false) {
            $content = str_replace('</body>', $tracking_pixel . '</body>', $content);
        } else {
            // If no body tag, append to end
            $content .= $tracking_pixel;
        }
        
        return $content;
    }
    
    /**
     * Add click tracking to links
     */
    private function add_click_tracking($content, $tracking_token)
    {
        // Find all links and add tracking
        $pattern = '/<a\s+(?:[^>]*?\s+)?href=(["\'])(.*?)\1/i';
        
        return preg_replace_callback($pattern, function($matches) use ($tracking_token) {
            $quote = $matches[1];
            $url = $matches[2];
            
            // Skip if it's already a tracking URL or unsubscribe URL
            if (strpos($url, 'em_click=') !== false || strpos($url, 'em_unsubscribe=') !== false) {
                return $matches[0];
            }
            
            // Create tracking URL
            $tracking_url = site_url('?em_click=' . $tracking_token . '&url=' . urlencode($url));
            
            return str_replace('href=' . $quote . $url . $quote, 'href=' . $quote . $tracking_url . $quote, $matches[0]);
        }, $content);
    }
    
    /**
     * Generate unsubscribe URL
     */
    private function generate_unsubscribe_url($recipient)
    {
        $token = isset($recipient->tracking_token) ? $recipient->tracking_token : 'unknown';
        return site_url('?em_unsubscribe=' . $token);
    }
    
    /**
     * Log email sent for analytics
     */
    private function log_email_sent($campaign, $recipient)
    {
        $this->CI->load->model('email_marketing/analytics_model');
        
        $analytics_data = [
            'campaign_id' => $campaign->id,
            'recipient_email' => $recipient->email,
            'event_type' => 'sent',
            'tracking_token' => isset($recipient->tracking_token) ? $recipient->tracking_token : null,
            'ip_address' => $this->CI->input->ip_address(),
            'user_agent' => $this->CI->input->user_agent()
        ];
        
        $this->CI->analytics_model->log_event($analytics_data);
    }
    
    /**
     * Log automation email sent for analytics
     */
    private function log_automation_email_sent($automation_rule, $recipient)
    {
        $this->CI->load->model('email_marketing/analytics_model');
        
        $analytics_data = [
            'automation_rule_id' => $automation_rule->id,
            'recipient_email' => $recipient->email,
            'event_type' => 'sent',
            'tracking_token' => isset($recipient->tracking_token) ? $recipient->tracking_token : null,
            'ip_address' => $this->CI->input->ip_address(),
            'user_agent' => $this->CI->input->user_agent()
        ];
        
        $this->CI->analytics_model->log_event($analytics_data);
    }
    
    /**
     * Get last error message
     */
    public function get_last_error()
    {
        return $this->last_error;
    }
    
    /**
     * Test SMTP configuration
     */
    public function test_smtp_config($smtp_config, $test_email)
    {
        try {
            $config = [
                'protocol' => 'smtp',
                'smtp_host' => $smtp_config->smtp_host,
                'smtp_port' => $smtp_config->smtp_port,
                'smtp_user' => $smtp_config->smtp_username,
                'smtp_pass' => $smtp_config->smtp_password,
                'smtp_crypto' => $smtp_config->smtp_encryption,
                'mailtype' => 'html',
                'charset' => 'utf-8',
                'newline' => "\r\n"
            ];
            
            $this->CI->load->library('email');
            $this->CI->email->initialize($config);
            
            $this->CI->email->from($smtp_config->from_email, $smtp_config->from_name);
            $this->CI->email->to($test_email);
            $this->CI->email->subject('SMTP Test Email - ' . get_option('companyname'));
            $this->CI->email->message('
                <h2>SMTP Configuration Test</h2>
                <p>This is a test email to verify your SMTP configuration is working correctly.</p>
                <p><strong>SMTP Host:</strong> ' . $smtp_config->smtp_host . '</p>
                <p><strong>SMTP Port:</strong> ' . $smtp_config->smtp_port . '</p>
                <p><strong>Encryption:</strong> ' . $smtp_config->smtp_encryption . '</p>
                <p><strong>Test Date:</strong> ' . date('Y-m-d H:i:s') . '</p>
                <hr>
                <p><em>Sent from Perfex CRM Email Marketing Module</em></p>
            ');
            
            $result = $this->CI->email->send();
            
            if (!$result) {
                $this->last_error = $this->CI->email->print_debugger();
            }
            
            return $result;
            
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }
}

