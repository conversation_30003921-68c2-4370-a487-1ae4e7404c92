<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Public Email Marketing Controller
 * Handles public-facing unsubscribe and preference pages
 */
class Email_marketing extends App_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('email_marketing/unsubscribe_model');
        $this->load->helper('email_marketing');
    }
    
    /**
     * Unsubscribe page
     */
    public function unsubscribe()
    {
        $token = $this->input->get('token');
        $type = $this->input->get('type') ?: 'all';
        
        if (!$token) {
            $this->show_error_page('Invalid unsubscribe link');
            return;
        }
        
        // Validate token
        if (!$this->unsubscribe_model->validate_token($token)) {
            $this->show_error_page('Invalid or expired unsubscribe link');
            return;
        }
        
        $data = [
            'token' => $token,
            'type' => $type,
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'processed' => false,
            'error' => null
        ];
        
        // Process unsubscribe if form submitted
        if ($this->input->post()) {
            $unsubscribe_type = $this->input->post('unsubscribe_type') ?: $type;
            $result = $this->unsubscribe_model->process_unsubscribe($token, $unsubscribe_type);
            
            $data['processed'] = true;
            $data['result'] = $result;
            
            if (!$result['success']) {
                $data['error'] = $result['message'];
            }
        }
        
        $this->load->view('email_marketing/public/unsubscribe', $data);
    }
    
    /**
     * Email preferences page
     */
    public function preferences()
    {
        $token = $this->input->get('token');
        
        if (!$token) {
            $this->show_error_page('Invalid preferences link');
            return;
        }
        
        // Validate token and get recipient info
        if (!$this->unsubscribe_model->validate_token($token)) {
            $this->show_error_page('Invalid or expired preferences link');
            return;
        }
        
        // Get recipient information
        $recipient = $this->get_recipient_by_token($token);
        if (!$recipient) {
            $this->show_error_page('Unable to load preferences');
            return;
        }
        
        $data = [
            'token' => $token,
            'email' => $recipient['email'],
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'updated' => false,
            'error' => null
        ];
        
        // Get current preferences
        $data['preferences'] = $this->unsubscribe_model->get_email_preferences($recipient['email']);
        
        // Process preferences update if form submitted
        if ($this->input->post()) {
            $new_preferences = [
                'marketing_emails' => $this->input->post('marketing_emails') ? 1 : 0,
                'transactional_emails' => $this->input->post('transactional_emails') ? 1 : 0,
                'newsletter' => $this->input->post('newsletter') ? 1 : 0,
                'promotions' => $this->input->post('promotions') ? 1 : 0,
                'updates' => $this->input->post('updates') ? 1 : 0
            ];
            
            $result = $this->unsubscribe_model->update_preferences($recipient['email'], $new_preferences);
            
            if ($result) {
                $data['updated'] = true;
                $data['preferences'] = $new_preferences;
            } else {
                $data['error'] = 'Failed to update preferences. Please try again.';
            }
        }
        
        $this->load->view('email_marketing/public/preferences', $data);
    }
    
    /**
     * Resubscribe page
     */
    public function resubscribe()
    {
        $token = $this->input->get('token');
        $email = $this->input->get('email');
        
        if (!$token && !$email) {
            $this->show_error_page('Invalid resubscribe link');
            return;
        }
        
        $data = [
            'token' => $token,
            'email' => $email,
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'processed' => false,
            'error' => null
        ];
        
        // Get email from token if not provided
        if ($token && !$email) {
            $recipient = $this->get_recipient_by_token($token);
            if ($recipient) {
                $data['email'] = $recipient['email'];
                $email = $recipient['email'];
            }
        }
        
        // Process resubscribe if form submitted
        if ($this->input->post()) {
            $email_to_resubscribe = $this->input->post('email') ?: $email;
            $resubscribe_type = $this->input->post('resubscribe_type') ?: 'all';
            
            if (filter_var($email_to_resubscribe, FILTER_VALIDATE_EMAIL)) {
                $result = $this->unsubscribe_model->resubscribe($email_to_resubscribe, $resubscribe_type);
                
                $data['processed'] = true;
                $data['success'] = $result;
                
                if (!$result) {
                    $data['error'] = 'Failed to resubscribe. Please try again.';
                }
            } else {
                $data['error'] = 'Please enter a valid email address.';
            }
        }
        
        $this->load->view('email_marketing/public/resubscribe', $data);
    }
    
    /**
     * GDPR data export request
     */
    public function data_export()
    {
        $data = [
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'requested' => false,
            'error' => null
        ];
        
        if ($this->input->post()) {
            $email = $this->input->post('email');
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $data['error'] = 'Please enter a valid email address.';
            } else {
                // Generate data export
                $export_data = $this->generate_data_export($email);
                
                if (!empty($export_data)) {
                    // Send export data via email or provide download
                    $this->send_data_export($email, $export_data);
                    $data['requested'] = true;
                } else {
                    $data['error'] = 'No data found for the provided email address.';
                }
            }
        }
        
        $this->load->view('email_marketing/public/data_export', $data);
    }
    
    /**
     * GDPR data deletion request
     */
    public function data_deletion()
    {
        $data = [
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'requested' => false,
            'error' => null
        ];
        
        if ($this->input->post()) {
            $email = $this->input->post('email');
            $confirm = $this->input->post('confirm_deletion');
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $data['error'] = 'Please enter a valid email address.';
            } elseif (!$confirm) {
                $data['error'] = 'Please confirm that you want to delete your data.';
            } else {
                // Process data deletion
                $result = $this->process_data_deletion($email);
                
                if ($result) {
                    $data['requested'] = true;
                } else {
                    $data['error'] = 'Failed to process deletion request. Please try again.';
                }
            }
        }
        
        $this->load->view('email_marketing/public/data_deletion', $data);
    }
    
    /**
     * Privacy policy page
     */
    public function privacy()
    {
        $data = [
            'company_name' => get_option('companyname'),
            'company_address' => get_option('company_address'),
            'company_email' => get_option('company_email'),
            'company_phone' => get_option('company_phonenumber'),
            'privacy_policy' => get_option('email_marketing_privacy_policy')
        ];
        
        $this->load->view('email_marketing/public/privacy', $data);
    }
    
    /**
     * Show error page
     */
    private function show_error_page($message)
    {
        $data = [
            'error_message' => $message,
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo')
        ];
        
        $this->load->view('email_marketing/public/error', $data);
    }
    
    /**
     * Get recipient by token
     */
    private function get_recipient_by_token($token)
    {
        // This method would be similar to the one in unsubscribe_model
        // but simplified for public use
        
        // Check campaign recipients
        $this->db->select('email, recipient_type, recipient_id');
        $this->db->where('tracking_token', $token);
        $recipient = $this->db->get(db_prefix() . 'email_marketing_campaign_recipients')->row_array();
        
        if ($recipient) {
            return $recipient;
        }
        
        // Check automation queue
        $this->db->select('email, recipient_type, recipient_id');
        $this->db->where('tracking_token', $token);
        $recipient = $this->db->get(db_prefix() . 'email_marketing_automation_queue')->row_array();
        
        if ($recipient) {
            return $recipient;
        }
        
        // Check lead response queue
        $this->db->select('recipient_email as email, "lead" as recipient_type, lead_id as recipient_id');
        $this->db->where('tracking_token', $token);
        $recipient = $this->db->get(db_prefix() . 'email_marketing_lead_response_queue')->row_array();
        
        return $recipient;
    }
    
    /**
     * Generate GDPR data export
     */
    private function generate_data_export($email)
    {
        $export_data = [];
        
        // Get unsubscribe data
        $unsubscribe_data = $this->unsubscribe_model->export_user_data($email);
        if (!empty($unsubscribe_data)) {
            $export_data['unsubscribe_data'] = $unsubscribe_data;
        }
        
        // Get email analytics data
        $this->load->model('email_marketing/analytics_model');
        $analytics_data = $this->analytics_model->export_user_data($email);
        if (!empty($analytics_data)) {
            $export_data['analytics_data'] = $analytics_data;
        }
        
        // Get campaign recipient data
        $this->db->where('email', $email);
        $campaign_data = $this->db->get(db_prefix() . 'email_marketing_campaign_recipients')->result_array();
        if (!empty($campaign_data)) {
            $export_data['campaign_data'] = $campaign_data;
        }
        
        return $export_data;
    }
    
    /**
     * Send data export to user
     */
    private function send_data_export($email, $export_data)
    {
        // Convert data to JSON format
        $json_data = json_encode($export_data, JSON_PRETTY_PRINT);
        
        // Create temporary file
        $temp_file = tempnam(sys_get_temp_dir(), 'gdpr_export_');
        file_put_contents($temp_file, $json_data);
        
        // Send email with attachment
        $this->load->library('email');
        
        $this->email->from(get_option('company_email'), get_option('companyname'));
        $this->email->to($email);
        $this->email->subject('Your Data Export - ' . get_option('companyname'));
        
        $message = "
        <h2>Your Data Export</h2>
        <p>As requested, please find attached your personal data that we have collected and processed.</p>
        <p>This export includes:</p>
        <ul>
            <li>Email preferences and unsubscribe history</li>
            <li>Email analytics data (opens, clicks)</li>
            <li>Campaign recipient information</li>
        </ul>
        <p>If you have any questions about this data or would like to request deletion, please contact us.</p>
        <p>Best regards,<br>" . get_option('companyname') . "</p>
        ";
        
        $this->email->message($message);
        $this->email->attach($temp_file, 'attachment', 'data_export_' . date('Y-m-d') . '.json');
        
        $result = $this->email->send();
        
        // Clean up temporary file
        unlink($temp_file);
        
        return $result;
    }
    
    /**
     * Process GDPR data deletion
     */
    private function process_data_deletion($email)
    {
        try {
            // Delete unsubscribe and preference data
            $this->unsubscribe_model->delete_user_data($email);
            
            // Delete analytics data
            $this->load->model('email_marketing/analytics_model');
            $this->analytics_model->delete_user_data($email);
            
            // Anonymize campaign recipient data (keep for statistics but remove personal info)
            $this->db->where('email', $email);
            $this->db->update(db_prefix() . 'email_marketing_campaign_recipients', [
                'email' => '<EMAIL>',
                'phone' => '',
                'name' => 'Deleted User'
            ]);
            
            // Log the deletion request
            log_activity('GDPR Data Deletion Request Processed for: ' . $email);
            
            return true;
        } catch (Exception $e) {
            log_activity('GDPR Data Deletion Failed for: ' . $email . ' - Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Subscription confirmation (double opt-in)
     */
    public function confirm()
    {
        $token = $this->input->get('token');
        
        if (!$token) {
            $this->show_error_page('Invalid confirmation link');
            return;
        }
        
        // Process confirmation
        $result = $this->process_subscription_confirmation($token);
        
        $data = [
            'company_name' => get_option('companyname'),
            'company_logo' => get_option('company_logo'),
            'confirmed' => $result,
            'error' => $result ? null : 'Invalid or expired confirmation link'
        ];
        
        $this->load->view('email_marketing/public/confirm', $data);
    }
    
    /**
     * Process subscription confirmation
     */
    private function process_subscription_confirmation($token)
    {
        // This would handle double opt-in confirmation
        // For now, return true as placeholder
        return true;
    }
    
    /**
     * Email marketing footer links
     */
    public function footer()
    {
        $data = [
            'company_name' => get_option('companyname'),
            'company_address' => get_option('company_address'),
            'company_email' => get_option('company_email'),
            'company_phone' => get_option('company_phonenumber')
        ];
        
        $this->load->view('email_marketing/public/footer', $data);
    }
}

