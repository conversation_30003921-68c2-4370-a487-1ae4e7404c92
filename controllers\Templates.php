<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Templates Controller
 */
class Templates extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        $this->load->model('templates_model');
        $this->load_module_language();
        
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
    }

    /**
     * Load module language file
     */
    private function load_module_language()
    {
        $language = $this->config->item('language');
        if (empty($language)) {
            $language = 'english';
        }

        $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/' . $language . '/email_marketing_lang.php';

        if (file_exists($lang_file)) {
            $this->lang->load('email_marketing_lang', $language, FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
        } else {
            // Fallback to english
            $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/english/email_marketing_lang.php';
            if (file_exists($lang_file)) {
                $this->lang->load('email_marketing_lang', 'english', FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
            }
        }
    }
    
    /**
     * Templates listing
     */
    public function index()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/templates/table'));
        }
        
        $data['title'] = _l('email_marketing_templates');
        $this->load->view('admin/email_marketing/templates/manage', $data);
    }
    
    /**
     * Create/Edit template
     */
    public function template($id = '')
    {
        if ($this->input->post()) {
            if ($id == '') {
                // Create new template
                if (!has_permission('email_marketing', '', 'create')) {
                    access_denied('email_marketing');
                }
                
                $template_id = $this->templates_model->add($this->input->post());
                
                if ($template_id) {
                    set_alert('success', _l('email_marketing_template_created'));
                    redirect(admin_url('email_marketing/templates/template/' . $template_id));
                } else {
                    set_alert('danger', 'Failed to create template.');
                }
            } else {
                // Update existing template
                if (!has_permission('email_marketing', '', 'edit')) {
                    access_denied('email_marketing');
                }
                
                $success = $this->templates_model->update($this->input->post(), $id);
                
                if ($success) {
                    set_alert('success', _l('email_marketing_template_updated'));
                } else {
                    set_alert('danger', 'Failed to update template.');
                }
                
                redirect(admin_url('email_marketing/templates/template/' . $id));
            }
        }
        
        if ($id == '') {
            $title = _l('email_marketing_new_template');
            $template = null;
        } else {
            $template = $this->templates_model->get($id);
            if (!$template) {
                show_404();
            }
            $title = _l('email_marketing_edit_template');
        }
        
        $data['template'] = $template;
        $data['title'] = $title;
        $data['merge_tags'] = $this->templates_model->get_available_merge_tags();
        
        $this->load->view('admin/email_marketing/templates/template', $data);
    }
    
    /**
     * Delete template
     */
    public function delete($id)
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        if (!$id) {
            redirect(admin_url('email_marketing/templates'));
        }
        
        $response = $this->templates_model->delete($id);
        
        if ($response === true) {
            set_alert('success', _l('email_marketing_template_deleted'));
        } elseif (is_array($response) && isset($response['error'])) {
            set_alert('warning', $response['error']);
        } else {
            set_alert('warning', 'Failed to delete template.');
        }
        
        redirect(admin_url('email_marketing/templates'));
    }
    
    /**
     * Duplicate template
     */
    public function duplicate($id)
    {
        if (!has_permission('email_marketing', '', 'create')) {
            access_denied('email_marketing');
        }
        
        $new_template_id = $this->templates_model->duplicate($id);
        
        if ($new_template_id) {
            set_alert('success', 'Template duplicated successfully.');
            redirect(admin_url('email_marketing/templates/template/' . $new_template_id));
        } else {
            set_alert('danger', 'Failed to duplicate template.');
            redirect(admin_url('email_marketing/templates'));
        }
    }
    
    /**
     * Preview template
     */
    public function preview($id)
    {
        $template = $this->templates_model->get($id);
        if (!$template) {
            show_404();
        }
        
        $preview_data = $this->templates_model->generate_preview($id);
        
        // Output the preview HTML
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Preview: ' . htmlspecialchars($template->name) . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .preview-header { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .preview-content { border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="preview-header">
        <h3>Template Preview: ' . htmlspecialchars($template->name) . '</h3>
        <p><strong>Subject:</strong> ' . htmlspecialchars($preview_data['subject']) . '</p>
        <p><em>This is a preview with sample data. Actual emails will use real recipient data.</em></p>
    </div>
    <div class="preview-content">
        ' . $preview_data['content'] . '
    </div>
</body>
</html>';
    }
    
    /**
     * Set template as default
     */
    public function set_default()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $template_id = $this->input->post('template_id');
        $template_type = $this->input->post('template_type');
        
        $success = $this->templates_model->set_as_default($template_id, $template_type);
        
        if ($success) {
            echo json_encode(['success' => true, 'message' => 'Template set as default successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to set template as default']);
        }
    }
    
    /**
     * Import template
     */
    public function import()
    {
        if (!has_permission('email_marketing', '', 'create')) {
            access_denied('email_marketing');
        }
        
        if ($this->input->post()) {
            $config['upload_path'] = get_temp_dir();
            $config['allowed_types'] = 'html|htm';
            $config['max_size'] = 2048; // 2MB
            $config['encrypt_name'] = true;
            
            $this->load->library('upload', $config);
            
            if ($this->upload->do_upload('template_file')) {
                $upload_data = $this->upload->data();
                
                $template_data = [
                    'name' => $this->input->post('template_name'),
                    'subject' => $this->input->post('template_subject'),
                    'template_type' => $this->input->post('template_type')
                ];
                
                $result = $this->templates_model->import_template($upload_data['full_path'], $template_data);
                
                // Clean up uploaded file
                unlink($upload_data['full_path']);
                
                if (isset($result['success']) && $result['success']) {
                    set_alert('success', 'Template imported successfully.');
                    redirect(admin_url('email_marketing/templates/template/' . $result['template_id']));
                } else {
                    set_alert('danger', $result['error']);
                }
            } else {
                set_alert('danger', $this->upload->display_errors());
            }
        }
        
        $data['title'] = 'Import Template';
        $this->load->view('admin/email_marketing/templates/import', $data);
    }
    
    /**
     * Export template
     */
    public function export($id)
    {
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
        
        $export_data = $this->templates_model->export_template($id);
        
        if ($export_data) {
            header('Content-Type: ' . $export_data['mime_type']);
            header('Content-Disposition: attachment; filename="' . $export_data['filename'] . '"');
            echo $export_data['content'];
        } else {
            show_404();
        }
    }
    
    /**
     * Validate template content
     */
    public function validate()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $content = $this->input->post('content');
        $validation_result = $this->templates_model->validate_template($content);
        
        header('Content-Type: application/json');
        echo json_encode($validation_result);
    }
    
    /**
     * Get template usage statistics
     */
    public function usage($id)
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $usage = $this->templates_model->get_template_usage($id);
        
        header('Content-Type: application/json');
        echo json_encode($usage);
    }
    
    /**
     * Search templates
     */
    public function search()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $search_term = $this->input->post('search_term');
        $filters = $this->input->post('filters');
        
        $results = $this->templates_model->search_templates($search_term, $filters);
        
        header('Content-Type: application/json');
        echo json_encode($results);
    }
    
    /**
     * Get merge tags for editor
     */
    public function get_merge_tags()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $merge_tags = $this->templates_model->get_available_merge_tags();
        
        header('Content-Type: application/json');
        echo json_encode($merge_tags);
    }
    
    /**
     * Clone template with modifications
     */
    public function clone_template()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'create')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $template_id = $this->input->post('template_id');
        $modifications = $this->input->post('modifications');
        
        $new_template_id = $this->templates_model->clone_template($template_id, $modifications);
        
        if ($new_template_id) {
            echo json_encode([
                'success' => true,
                'template_id' => $new_template_id,
                'message' => 'Template cloned successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to clone template'
            ]);
        }
    }
    
    /**
     * Get templates by type (for AJAX)
     */
    public function get_by_type()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $type = $this->input->post('type');
        $templates = $this->templates_model->get_by_type($type);
        
        header('Content-Type: application/json');
        echo json_encode($templates);
    }
}

