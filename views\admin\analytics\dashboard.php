<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-bar-chart"></i> <?php echo _l('email_marketing_analytics'); ?>
                </h3>
                <div class="panel-options">
                    <div class="btn-group">
                        <button class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                            <i class="fa fa-download"></i> <?php echo _l('email_marketing_export'); ?> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="#" onclick="exportData('pdf')"><i class="fa fa-file-pdf-o"></i> PDF Report</a></li>
                            <li><a href="#" onclick="exportData('excel')"><i class="fa fa-file-excel-o"></i> Excel Report</a></li>
                            <li><a href="#" onclick="exportData('csv')"><i class="fa fa-file-text-o"></i> CSV Data</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Filters -->
<div class="row">
    <div class="col-md-12">
        <div class="panel panel-flat">
            <div class="panel-body">
                <div class="analytics-filters">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_date_range'); ?></label>
                                <select class="form-control selectpicker" id="date-range-select">
                                    <option value="7"><?php echo _l('email_marketing_last_7_days'); ?></option>
                                    <option value="30" selected><?php echo _l('email_marketing_last_30_days'); ?></option>
                                    <option value="90"><?php echo _l('email_marketing_last_90_days'); ?></option>
                                    <option value="365"><?php echo _l('email_marketing_last_year'); ?></option>
                                    <option value="custom"><?php echo _l('email_marketing_custom_range'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3" id="custom-date-range" style="display: none;">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_custom_date_range'); ?></label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" id="custom-date-from" placeholder="From">
                                    <span class="input-group-addon">-</span>
                                    <input type="text" class="form-control datepicker" id="custom-date-to" placeholder="To">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo _l('email_marketing_campaign_filter'); ?></label>
                                <select class="form-control selectpicker" id="campaign-filter" data-live-search="true">
                                    <option value=""><?php echo _l('email_marketing_all_campaigns'); ?></option>
                                    <!-- Campaigns will be loaded via AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button class="btn btn-primary btn-block" onclick="updateAnalytics()">
                                    <i class="fa fa-refresh"></i> <?php echo _l('email_marketing_update'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-lg-3 col-md-6">
        <div class="metric-card">
            <div class="metric-icon bg-primary">
                <i class="fa fa-paper-plane"></i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value" id="total-sent">0</h3>
                <p class="metric-label"><?php echo _l('email_marketing_total_sent'); ?></p>
                <div class="metric-change">
                    <span class="change-indicator" id="sent-change">+0%</span>
                    <span class="change-period">vs previous period</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="metric-card">
            <div class="metric-icon bg-success">
                <i class="fa fa-envelope-open"></i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value" id="total-opens">0</h3>
                <p class="metric-label"><?php echo _l('email_marketing_total_opens'); ?></p>
                <div class="metric-change">
                    <span class="change-indicator" id="opens-change">+0%</span>
                    <span class="change-period">vs previous period</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="metric-card">
            <div class="metric-icon bg-info">
                <i class="fa fa-mouse-pointer"></i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value" id="total-clicks">0</h3>
                <p class="metric-label"><?php echo _l('email_marketing_total_clicks'); ?></p>
                <div class="metric-change">
                    <span class="change-indicator" id="clicks-change">+0%</span>
                    <span class="change-period">vs previous period</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="metric-card">
            <div class="metric-icon bg-warning">
                <i class="fa fa-user-times"></i>
            </div>
            <div class="metric-content">
                <h3 class="metric-value" id="total-unsubscribes">0</h3>
                <p class="metric-label"><?php echo _l('email_marketing_unsubscribes'); ?></p>
                <div class="metric-change">
                    <span class="change-indicator" id="unsubscribes-change">+0%</span>
                    <span class="change-period">vs previous period</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <div class="col-md-8">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-line-chart"></i> <?php echo _l('email_marketing_performance_trends'); ?>
                </h3>
                <div class="panel-options">
                    <div class="btn-group btn-group-sm" data-toggle="buttons">
                        <label class="btn btn-default active">
                            <input type="radio" name="chart-metric" value="all" checked> All Metrics
                        </label>
                        <label class="btn btn-default">
                            <input type="radio" name="chart-metric" value="opens"> Opens
                        </label>
                        <label class="btn btn-default">
                            <input type="radio" name="chart-metric" value="clicks"> Clicks
                        </label>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <canvas id="performance-trends-chart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-pie-chart"></i> <?php echo _l('email_marketing_engagement_breakdown'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <canvas id="engagement-chart" height="250"></canvas>
                <div class="engagement-legend">
                    <div class="legend-item">
                        <span class="legend-color bg-success"></span>
                        <span class="legend-label">Opened</span>
                        <span class="legend-value" id="opened-percentage">0%</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color bg-info"></span>
                        <span class="legend-label">Clicked</span>
                        <span class="legend-value" id="clicked-percentage">0%</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color bg-warning"></span>
                        <span class="legend-label">Unsubscribed</span>
                        <span class="legend-value" id="unsubscribed-percentage">0%</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-color bg-muted"></span>
                        <span class="legend-label">No Action</span>
                        <span class="legend-value" id="no-action-percentage">0%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Analytics -->
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-trophy"></i> <?php echo _l('email_marketing_top_performing_campaigns'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <div class="top-campaigns-list" id="top-campaigns">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-clock-o"></i> <?php echo _l('email_marketing_recent_activity'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <div class="recent-activity-list" id="recent-activity">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Geographic and Device Analytics -->
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-globe"></i> <?php echo _l('email_marketing_geographic_distribution'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <canvas id="geographic-chart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-mobile"></i> <?php echo _l('email_marketing_device_breakdown'); ?>
                </h3>
            </div>
            <div class="panel-body">
                <canvas id="device-chart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<style>
/* Analytics Dashboard Styles */
.analytics-filters {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-bottom: 15px;
    position: relative;
}

.metric-icon::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.2;
    z-index: -1;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin: 0 0 5px 0;
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.metric-label {
    color: #666;
    font-size: 14px;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: 8px;
}

.change-indicator {
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.change-indicator.positive {
    background: #d4edda;
    color: #155724;
}

.change-indicator.negative {
    background: #f8d7da;
    color: #721c24;
}

.change-period {
    font-size: 12px;
    color: #999;
}

.engagement-legend {
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.legend-item:last-child {
    border-bottom: none;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
}

.legend-label {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.legend-value {
    font-weight: 600;
    color: #666;
}

.top-campaigns-list .campaign-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.campaign-item:hover {
    background: #f8f9fa;
    padding-left: 10px;
    border-radius: 8px;
}

.campaign-item:last-child {
    border-bottom: none;
}

.campaign-info h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.campaign-meta {
    font-size: 12px;
    color: #666;
}

.campaign-stats {
    text-align: right;
}

.campaign-rate {
    font-size: 16px;
    font-weight: 600;
    color: #007bff;
}

.campaign-count {
    font-size: 12px;
    color: #666;
}

.recent-activity-list .activity-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 16px;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0 0 3px 0;
}

.activity-time {
    font-size: 12px;
    color: #666;
}

/* Chart containers */
.panel-body canvas {
    max-height: 400px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metric-card {
        text-align: center;
    }
    
    .analytics-filters .row > div {
        margin-bottom: 15px;
    }
    
    .metric-change {
        justify-content: center;
    }
}

/* Loading states */
.loading-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #666;
}

.loading-chart i {
    font-size: 24px;
    margin-right: 10px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize date pickers
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true
    });
    
    // Date range selector
    $('#date-range-select').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom-date-range').show();
        } else {
            $('#custom-date-range').hide();
            updateAnalytics();
        }
    });
    
    // Chart metric selector
    $('input[name="chart-metric"]').change(function() {
        updatePerformanceTrends();
    });
    
    // Load initial data
    loadCampaignOptions();
    updateAnalytics();
});

function updateAnalytics() {
    const dateRange = $('#date-range-select').val();
    const campaignId = $('#campaign-filter').val();
    
    let params = {
        date_range: dateRange,
        campaign_id: campaignId
    };
    
    if (dateRange === 'custom') {
        params.date_from = $('#custom-date-from').val();
        params.date_to = $('#custom-date-to').val();
    }
    
    // Load all analytics data
    loadKeyMetrics(params);
    loadPerformanceTrends(params);
    loadEngagementChart(params);
    loadTopCampaigns(params);
    loadRecentActivity(params);
    loadGeographicData(params);
    loadDeviceData(params);
}

function loadKeyMetrics(params) {
    $.get(admin_url + 'email_marketing/analytics/key_metrics', params, function(data) {
        // Animate counter updates
        animateCounter('#total-sent', data.total_sent);
        animateCounter('#total-opens', data.total_opens);
        animateCounter('#total-clicks', data.total_clicks);
        animateCounter('#total-unsubscribes', data.total_unsubscribes);
        
        // Update change indicators
        updateChangeIndicator('#sent-change', data.sent_change);
        updateChangeIndicator('#opens-change', data.opens_change);
        updateChangeIndicator('#clicks-change', data.clicks_change);
        updateChangeIndicator('#unsubscribes-change', data.unsubscribes_change);
    });
}

function loadPerformanceTrends(params) {
    const metric = $('input[name="chart-metric"]:checked').val();
    params.metric = metric;
    
    $.get(admin_url + 'email_marketing/analytics/performance_trends', params, function(data) {
        const ctx = document.getElementById('performance-trends-chart').getContext('2d');
        
        if (window.performanceTrendsChart) {
            window.performanceTrendsChart.destroy();
        }
        
        const datasets = [];
        
        if (metric === 'all' || metric === 'opens') {
            datasets.push({
                label: 'Opens',
                data: data.opens,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            });
        }
        
        if (metric === 'all' || metric === 'clicks') {
            datasets.push({
                label: 'Clicks',
                data: data.clicks,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            });
        }
        
        if (metric === 'all') {
            datasets.push({
                label: 'Sent',
                data: data.sent,
                borderColor: '#6c757d',
                backgroundColor: 'rgba(108, 117, 125, 0.1)',
                tension: 0.4,
                fill: false
            });
        }
        
        window.performanceTrendsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255,255,255,0.1)',
                        borderWidth: 1
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    });
}

function loadEngagementChart(params) {
    $.get(admin_url + 'email_marketing/analytics/engagement_breakdown', params, function(data) {
        const ctx = document.getElementById('engagement-chart').getContext('2d');
        
        if (window.engagementChart) {
            window.engagementChart.destroy();
        }
        
        window.engagementChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Opened', 'Clicked', 'Unsubscribed', 'No Action'],
                datasets: [{
                    data: [data.opened, data.clicked, data.unsubscribed, data.no_action],
                    backgroundColor: [
                        '#28a745',
                        '#007bff',
                        '#ffc107',
                        '#6c757d'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: 'white'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + percentage + '%';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
        
        // Update legend percentages
        const total = data.opened + data.clicked + data.unsubscribed + data.no_action;
        $('#opened-percentage').text(((data.opened / total) * 100).toFixed(1) + '%');
        $('#clicked-percentage').text(((data.clicked / total) * 100).toFixed(1) + '%');
        $('#unsubscribed-percentage').text(((data.unsubscribed / total) * 100).toFixed(1) + '%');
        $('#no-action-percentage').text(((data.no_action / total) * 100).toFixed(1) + '%');
    });
}

function loadTopCampaigns(params) {
    $.get(admin_url + 'email_marketing/analytics/top_campaigns', params, function(data) {
        let html = '';
        
        if (data.length > 0) {
            data.forEach(function(campaign, index) {
                html += `
                    <div class="campaign-item">
                        <div class="campaign-info">
                            <h5>${campaign.name}</h5>
                            <div class="campaign-meta">
                                ${campaign.sent_count} sent • ${campaign.sent_at}
                            </div>
                        </div>
                        <div class="campaign-stats">
                            <div class="campaign-rate">${campaign.open_rate}%</div>
                            <div class="campaign-count">${campaign.opens_count} opens</div>
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<div class="text-center text-muted">No campaigns found</div>';
        }
        
        $('#top-campaigns').html(html);
    });
}

function loadRecentActivity(params) {
    $.get(admin_url + 'email_marketing/analytics/recent_activity', params, function(data) {
        let html = '';
        
        if (data.length > 0) {
            data.forEach(function(activity) {
                const iconClass = getActivityIcon(activity.event_type);
                const iconColor = getActivityColor(activity.event_type);
                
                html += `
                    <div class="activity-item">
                        <div class="activity-icon ${iconColor}">
                            <i class="fa ${iconClass}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.description}</div>
                            <div class="activity-time">${activity.time_ago}</div>
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<div class="text-center text-muted">No recent activity</div>';
        }
        
        $('#recent-activity').html(html);
    });
}

function loadGeographicData(params) {
    $.get(admin_url + 'email_marketing/analytics/geographic_data', params, function(data) {
        const ctx = document.getElementById('geographic-chart').getContext('2d');
        
        if (window.geographicChart) {
            window.geographicChart.destroy();
        }
        
        window.geographicChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.countries,
                datasets: [{
                    label: 'Opens by Country',
                    data: data.opens,
                    backgroundColor: 'rgba(0, 123, 255, 0.8)',
                    borderColor: '#007bff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
}

function loadDeviceData(params) {
    $.get(admin_url + 'email_marketing/analytics/device_data', params, function(data) {
        const ctx = document.getElementById('device-chart').getContext('2d');
        
        if (window.deviceChart) {
            window.deviceChart.destroy();
        }
        
        window.deviceChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.devices,
                datasets: [{
                    data: data.counts,
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#dc3545',
                        '#6f42c1'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
}

function loadCampaignOptions() {
    $.get(admin_url + 'email_marketing/campaigns/get_options', function(data) {
        let options = '<option value="">All Campaigns</option>';
        data.forEach(function(campaign) {
            options += `<option value="${campaign.id}">${campaign.name}</option>`;
        });
        $('#campaign-filter').html(options).selectpicker('refresh');
    });
}

function animateCounter(selector, endValue) {
    const element = $(selector);
    const startValue = 0;
    const duration = 1500;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(startValue + (endValue - startValue) * progress);
        element.text(currentValue.toLocaleString());
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.text(endValue.toLocaleString());
        }
    }
    
    requestAnimationFrame(updateCounter);
}

function updateChangeIndicator(selector, change) {
    const element = $(selector);
    const isPositive = change >= 0;
    
    element.removeClass('positive negative');
    element.addClass(isPositive ? 'positive' : 'negative');
    element.text((isPositive ? '+' : '') + change.toFixed(1) + '%');
}

function getActivityIcon(eventType) {
    const icons = {
        'sent': 'fa-paper-plane',
        'opened': 'fa-envelope-open',
        'clicked': 'fa-mouse-pointer',
        'unsubscribed': 'fa-user-times',
        'bounced': 'fa-exclamation-triangle'
    };
    return icons[eventType] || 'fa-circle';
}

function getActivityColor(eventType) {
    const colors = {
        'sent': 'bg-primary',
        'opened': 'bg-success',
        'clicked': 'bg-info',
        'unsubscribed': 'bg-warning',
        'bounced': 'bg-danger'
    };
    return colors[eventType] || 'bg-secondary';
}

function exportData(format) {
    const params = {
        format: format,
        date_range: $('#date-range-select').val(),
        campaign_id: $('#campaign-filter').val()
    };
    
    if (params.date_range === 'custom') {
        params.date_from = $('#custom-date-from').val();
        params.date_to = $('#custom-date-to').val();
    }
    
    window.open(admin_url + 'email_marketing/analytics/export?' + $.param(params));
}
</script>

