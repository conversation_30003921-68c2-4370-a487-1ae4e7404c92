<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * SMTP Model
 */
class Smtp_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_smtp_configs';
    }
    
    /**
     * Get all SMTP configurations
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('is_default', 'DESC');
        $this->db->order_by('name');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single SMTP configuration
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        $config = $this->db->get($this->table)->row();
        
        if ($config && $config->smtp_password) {
            // Decrypt password
            $config->smtp_password = $this->decrypt_password($config->smtp_password);
        }
        
        return $config;
    }
    
    /**
     * Get default SMTP configuration
     */
    public function get_default_config()
    {
        $this->db->where('is_default', 1);
        $this->db->where('is_active', 1);
        $config = $this->db->get($this->table)->row();
        
        if ($config && $config->smtp_password) {
            // Decrypt password
            $config->smtp_password = $this->decrypt_password($config->smtp_password);
        }
        
        return $config;
    }
    
    /**
     * Add new SMTP configuration
     */
    public function add($data)
    {
        // Encrypt password
        if (isset($data['smtp_password'])) {
            $data['smtp_password'] = $this->encrypt_password($data['smtp_password']);
        }
        
        // If this is set as default, remove default from others
        if (isset($data['is_default']) && $data['is_default'] == 1) {
            $this->remove_default_status();
        }
        
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            log_activity('New SMTP Configuration Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update SMTP configuration
     */
    public function update($data, $id)
    {
        // Encrypt password if provided
        if (isset($data['smtp_password']) && !empty($data['smtp_password'])) {
            $data['smtp_password'] = $this->encrypt_password($data['smtp_password']);
        } else {
            // Don't update password if not provided
            unset($data['smtp_password']);
        }
        
        // If this is set as default, remove default from others
        if (isset($data['is_default']) && $data['is_default'] == 1) {
            $this->remove_default_status();
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            log_activity('SMTP Configuration Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete SMTP configuration
     */
    public function delete($id)
    {
        $config = $this->get($id);
        if (!$config) {
            return false;
        }
        
        // Check if this is the default configuration
        if ($config->is_default == 1) {
            return ['error' => 'Cannot delete the default SMTP configuration. Please set another configuration as default first.'];
        }
        
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('SMTP Configuration Deleted [ID: ' . $id . ', Name: ' . $config->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Set configuration as default
     */
    public function set_as_default($id)
    {
        // Remove default status from all configurations
        $this->remove_default_status();
        
        // Set the selected configuration as default
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_default' => 1]);
    }
    
    /**
     * Remove default status from all configurations
     */
    private function remove_default_status()
    {
        $this->db->update($this->table, ['is_default' => 0]);
    }
    
    /**
     * Test SMTP configuration
     */
    public function test_configuration($id, $test_email)
    {
        $config = $this->get($id);
        if (!$config) {
            return ['success' => false, 'message' => 'SMTP configuration not found'];
        }
        
        $this->load->library('email_marketing/email_sender');
        $result = $this->email_sender->test_smtp_config($config, $test_email);
        
        if ($result) {
            return ['success' => true, 'message' => 'Test email sent successfully'];
        } else {
            return ['success' => false, 'message' => $this->email_sender->get_last_error()];
        }
    }
    
    /**
     * Get active configurations
     */
    public function get_active_configs()
    {
        $this->db->where('is_active', 1);
        $this->db->order_by('is_default', 'DESC');
        $this->db->order_by('name');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Increment sending counters
     */
    public function increment_counters($id)
    {
        $today = date('Y-m-d');
        $current_hour = date('Y-m-d H:00:00');
        
        // Get current config
        $this->db->where('id', $id);
        $config = $this->db->get($this->table)->row();
        
        if (!$config) {
            return false;
        }
        
        $update_data = [];
        
        // Reset daily counter if needed
        if ($config->last_reset_date != $today) {
            $update_data['sent_today'] = 1;
            $update_data['last_reset_date'] = $today;
        } else {
            $update_data['sent_today'] = $config->sent_today + 1;
        }
        
        // Reset hourly counter if needed
        if ($config->last_reset_hour != $current_hour) {
            $update_data['sent_this_hour'] = 1;
            $update_data['last_reset_hour'] = $current_hour;
        } else {
            $update_data['sent_this_hour'] = $config->sent_this_hour + 1;
        }
        
        $this->db->where('id', $id);
        return $this->db->update($this->table, $update_data);
    }
    
    /**
     * Reset daily counter
     */
    public function reset_daily_counter($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, [
            'sent_today' => 0,
            'last_reset_date' => date('Y-m-d')
        ]);
    }
    
    /**
     * Reset hourly counter
     */
    public function reset_hourly_counter($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, [
            'sent_this_hour' => 0,
            'last_reset_hour' => date('Y-m-d H:00:00')
        ]);
    }
    
    /**
     * Get sending statistics
     */
    public function get_sending_stats($id)
    {
        $this->db->where('id', $id);
        $config = $this->db->get($this->table)->row();
        
        if (!$config) {
            return false;
        }
        
        $stats = [
            'daily_sent' => $config->sent_today,
            'daily_limit' => $config->daily_limit,
            'daily_remaining' => max(0, $config->daily_limit - $config->sent_today),
            'hourly_sent' => $config->sent_this_hour,
            'hourly_limit' => $config->hourly_limit,
            'hourly_remaining' => max(0, $config->hourly_limit - $config->sent_this_hour),
            'last_reset_date' => $config->last_reset_date,
            'last_reset_hour' => $config->last_reset_hour
        ];
        
        // Calculate percentages
        if ($config->daily_limit > 0) {
            $stats['daily_usage_percent'] = round(($config->sent_today / $config->daily_limit) * 100, 2);
        } else {
            $stats['daily_usage_percent'] = 0;
        }
        
        if ($config->hourly_limit > 0) {
            $stats['hourly_usage_percent'] = round(($config->sent_this_hour / $config->hourly_limit) * 100, 2);
        } else {
            $stats['hourly_usage_percent'] = 0;
        }
        
        return $stats;
    }
    
    /**
     * Check if configuration can send emails
     */
    public function can_send_email($id)
    {
        $config = $this->get($id);
        if (!$config || !$config->is_active) {
            return false;
        }
        
        $today = date('Y-m-d');
        $current_hour = date('Y-m-d H:00:00');
        
        // Check daily limit
        if ($config->daily_limit > 0) {
            if ($config->last_reset_date != $today) {
                // Reset needed, can send
                return true;
            } elseif ($config->sent_today >= $config->daily_limit) {
                return false;
            }
        }
        
        // Check hourly limit
        if ($config->hourly_limit > 0) {
            if ($config->last_reset_hour != $current_hour) {
                // Reset needed, can send
                return true;
            } elseif ($config->sent_this_hour >= $config->hourly_limit) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get best available SMTP configuration for sending
     */
    public function get_best_config_for_sending()
    {
        // Get all active configurations ordered by availability
        $this->db->where('is_active', 1);
        $this->db->order_by('is_default', 'DESC');
        $configs = $this->db->get($this->table)->result();
        
        foreach ($configs as $config) {
            if ($this->can_send_email($config->id)) {
                // Decrypt password
                if ($config->smtp_password) {
                    $config->smtp_password = $this->decrypt_password($config->smtp_password);
                }
                return $config;
            }
        }
        
        return null; // No available configuration
    }
    
    /**
     * Encrypt password
     */
    private function encrypt_password($password)
    {
        // Use Perfex CRM's encryption if available, otherwise use base64
        if (function_exists('app_encrypt')) {
            return app_encrypt($password);
        } else {
            return base64_encode($password);
        }
    }
    
    /**
     * Decrypt password
     */
    private function decrypt_password($encrypted_password)
    {
        // Use Perfex CRM's decryption if available, otherwise use base64
        if (function_exists('app_decrypt')) {
            return app_decrypt($encrypted_password);
        } else {
            return base64_decode($encrypted_password);
        }
    }
    
    /**
     * Validate SMTP configuration
     */
    public function validate_config($data)
    {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors[] = 'Configuration name is required';
        }
        
        if (empty($data['smtp_host'])) {
            $errors[] = 'SMTP host is required';
        }
        
        if (empty($data['smtp_port']) || !is_numeric($data['smtp_port'])) {
            $errors[] = 'Valid SMTP port is required';
        }
        
        if (empty($data['smtp_username'])) {
            $errors[] = 'SMTP username is required';
        }
        
        if (empty($data['from_email']) || !filter_var($data['from_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid from email is required';
        }
        
        if (empty($data['from_name'])) {
            $errors[] = 'From name is required';
        }
        
        if (!empty($data['reply_to_email']) && !filter_var($data['reply_to_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Reply-to email must be valid if provided';
        }
        
        if (!empty($data['daily_limit']) && (!is_numeric($data['daily_limit']) || $data['daily_limit'] < 0)) {
            $errors[] = 'Daily limit must be a positive number';
        }
        
        if (!empty($data['hourly_limit']) && (!is_numeric($data['hourly_limit']) || $data['hourly_limit'] < 0)) {
            $errors[] = 'Hourly limit must be a positive number';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Get SMTP configurations statistics
     */
    public function get_smtp_stats()
    {
        $total_configs = $this->db->count_all_results($this->table);
        
        $this->db->where('is_active', 1);
        $active_configs = $this->db->count_all_results($this->table);
        
        $this->db->where('is_default', 1);
        $default_configs = $this->db->count_all_results($this->table);
        
        // Get total emails sent today across all configs
        $this->db->select('SUM(sent_today) as total_sent_today');
        $this->db->where('last_reset_date', date('Y-m-d'));
        $result = $this->db->get($this->table)->row();
        $total_sent_today = $result ? $result->total_sent_today : 0;
        
        return [
            'total_configs' => $total_configs,
            'active_configs' => $active_configs,
            'default_configs' => $default_configs,
            'total_sent_today' => $total_sent_today
        ];
    }
    
    /**
     * Duplicate SMTP configuration
     */
    public function duplicate($id)
    {
        $config = $this->get($id);
        if (!$config) {
            return false;
        }
        
        $new_data = [
            'name' => $config->name . ' (Copy)',
            'smtp_host' => $config->smtp_host,
            'smtp_port' => $config->smtp_port,
            'smtp_username' => $config->smtp_username,
            'smtp_password' => $config->smtp_password, // Will be re-encrypted in add()
            'smtp_encryption' => $config->smtp_encryption,
            'from_email' => $config->from_email,
            'from_name' => $config->from_name,
            'reply_to_email' => $config->reply_to_email,
            'is_default' => 0,
            'is_active' => 1,
            'daily_limit' => $config->daily_limit,
            'hourly_limit' => $config->hourly_limit
        ];
        
        return $this->add($new_data);
    }
}

