<?php

/**
 * Ensures that the module init file can't be accessed directly, only within the application.
 */
defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Email Marketing & Automation
Description: Comprehensive email marketing and automation module for Perfex CRM with advanced targeting, campaign management, SMTP/IMAP integration, and GDPR compliance.
Version: 1.0.0
Requires at least: 2.3.*
Author: Manus AI
Author URI: https://manus.ai
*/

/**
 * Define module constants first
 */
define('EMAIL_MARKETING_MODULE_NAME', 'email_marketing');
define('EMAIL_MARKETING_MODULE_PATH', dirname(__FILE__));
define('EMAIL_MARKETING_MODULE_URL', module_dir_url(EMAIL_MARKETING_MODULE_NAME));
define('EMAIL_MARKETING_VERSION', '1.0.0');

/**
 * Register activation hook
 */
register_activation_hook(EMAIL_MARKETING_MODULE_NAME, 'email_marketing_activation_hook');

function email_marketing_activation_hook()
{
    $CI = &get_instance();
    require_once(__DIR__ . '/install.php');
}

/**
 * Register deactivation hook
 */
register_deactivation_hook(EMAIL_MARKETING_MODULE_NAME, 'email_marketing_deactivation_hook');

function email_marketing_deactivation_hook()
{
    // Deactivation logic here
    update_option('email_marketing_module_active', 0);
}

/**
 * Register uninstall hook
 */
register_uninstall_hook(EMAIL_MARKETING_MODULE_NAME, 'email_marketing_uninstall_hook');

function email_marketing_uninstall_hook()
{
    $CI = &get_instance();
    require_once(__DIR__ . '/uninstall.php');
}

/**
 * Initialize the module
 */
hooks()->add_action('admin_init', 'email_marketing_init_menu_items');
hooks()->add_action('admin_init', 'email_marketing_permissions');
hooks()->add_action('after_lead_added', 'email_marketing_lead_added_trigger');
hooks()->add_action('after_lead_status_changed', 'email_marketing_lead_status_changed_trigger');
hooks()->add_action('after_customer_added', 'email_marketing_customer_added_trigger');

/**
 * Initialize menu items
 */
function email_marketing_init_menu_items()
{
    $CI = &get_instance();
    
    if (has_permission('email_marketing', '', 'view')) {
        $CI->app_menu->add_sidebar_menu_item('email_marketing', [
            'name'     => _l('email_marketing'),
            'href'     => admin_url('email_marketing'),
            'position' => 35,
            'icon'     => 'fa fa-envelope',
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_dashboard',
            'name'     => _l('email_marketing_dashboard'),
            'href'     => admin_url('email_marketing'),
            'position' => 1,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_campaigns',
            'name'     => _l('email_marketing_campaigns'),
            'href'     => admin_url('email_marketing/campaigns'),
            'position' => 2,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_templates',
            'name'     => _l('email_marketing_templates'),
            'href'     => admin_url('email_marketing/templates'),
            'position' => 3,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_automation',
            'name'     => _l('email_marketing_automation'),
            'href'     => admin_url('email_marketing/automation'),
            'position' => 4,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_analytics',
            'name'     => _l('email_marketing_analytics'),
            'href'     => admin_url('email_marketing/analytics'),
            'position' => 5,
        ]);
        
        $CI->app_menu->add_sidebar_children_item('email_marketing', [
            'slug'     => 'email_marketing_settings',
            'name'     => _l('email_marketing_settings'),
            'href'     => admin_url('email_marketing/settings'),
            'position' => 6,
        ]);
    }
}

/**
 * Initialize permissions
 */
function email_marketing_permissions()
{
    $capabilities = [];
    
    $capabilities['capabilities'] = [
        'view'   => _l('permission_view') . '(' . _l('permission_global') . ')',
        'create' => _l('permission_create'),
        'edit'   => _l('permission_edit'),
        'delete' => _l('permission_delete'),
    ];
    
    register_staff_capabilities('email_marketing', $capabilities, _l('email_marketing'));
}

/**
 * Trigger automation when lead is added
 */
function email_marketing_lead_added_trigger($lead_id)
{
    $CI = &get_instance();
    $CI->load->model('automation_model');
    $CI->automation_model->process_trigger('lead_created', $lead_id);
}

/**
 * Trigger automation when lead status changes
 */
function email_marketing_lead_status_changed_trigger($data)
{
    $CI = &get_instance();
    $CI->load->model('automation_model');
    $CI->automation_model->process_trigger('lead_status_changed', $data['lead_id'], $data);
}

/**
 * Trigger automation when customer is added
 */
function email_marketing_customer_added_trigger($customer_id)
{
    $CI = &get_instance();
    $CI->load->model('automation_model');
    $CI->automation_model->process_trigger('customer_created', $customer_id);
}

/**
 * Load module language files
 */
hooks()->add_action('app_admin_head', 'email_marketing_load_language');
hooks()->add_action('app_init', 'email_marketing_load_language');

function email_marketing_load_language()
{
    $CI = &get_instance();

    // Get current language
    $language = $CI->config->item('language');
    if (empty($language)) {
        $language = 'english';
    }

    // Load language file from module directory
    $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/' . $language . '/email_marketing_lang.php';

    if (file_exists($lang_file)) {
        $CI->lang->load('email_marketing_lang', $language, FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
    } else {
        // Fallback to english if current language file doesn't exist
        $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/english/email_marketing_lang.php';
        if (file_exists($lang_file)) {
            $CI->lang->load('email_marketing_lang', 'english', FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
        }
    }
}

/**
 * Add module assets
 */
hooks()->add_action('app_admin_head', 'email_marketing_add_head_components');

function email_marketing_add_head_components()
{
    $CI = &get_instance();
    
    // Add CSS
    echo '<link href="' . module_dir_url(EMAIL_MARKETING_MODULE_NAME, 'assets/css/email_marketing.css') . '?v=' . EMAIL_MARKETING_VERSION . '" rel="stylesheet" type="text/css" />';
    
    // Add JavaScript
    echo '<script src="' . module_dir_url(EMAIL_MARKETING_MODULE_NAME, 'assets/js/email_marketing.js') . '?v=' . EMAIL_MARKETING_VERSION . '"></script>';
}

/**
 * Add cron job for processing automation queue
 */
hooks()->add_action('after_cron_run', 'email_marketing_process_automation_queue');

function email_marketing_process_automation_queue()
{
    $CI = &get_instance();
    $CI->load->model('automation_model');
    $CI->automation_model->process_queue();
}

/**
 * Add tracking pixel for email opens
 */
hooks()->add_action('init', 'email_marketing_handle_tracking');

function email_marketing_handle_tracking()
{
    $CI = &get_instance();
    
    if ($CI->input->get('em_track')) {
        $CI->load->model('analytics_model');
        $CI->analytics_model->track_email_open($CI->input->get('em_track'));

        // Output 1x1 transparent pixel
        header('Content-Type: image/gif');
        echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
        exit;
    }
    
    if ($CI->input->get('em_click')) {
        $CI->load->model('analytics_model');
        $url = $CI->analytics_model->track_email_click($CI->input->get('em_click'));

        if ($url) {
            redirect($url);
        }
    }
    
    if ($CI->input->get('em_unsubscribe')) {
        // Handle unsubscribe directly or redirect to unsubscribe page
        redirect(site_url('email_marketing/unsubscribe/' . $CI->input->get('em_unsubscribe')));
    }
}

