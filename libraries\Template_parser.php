<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Template Parser Library
 * Handles parsing of email templates with merge tags and dynamic content
 */
class Template_parser
{
    private $CI;
    private $merge_tags;
    
    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->config('email_marketing/email_marketing_config');
        $this->merge_tags = $this->CI->config->item('email_marketing')['merge_tags'];
    }
    
    /**
     * Parse template content with data
     */
    public function parse($content, $data = [])
    {
        if (empty($content)) {
            return $content;
        }
        
        // Parse simple merge tags
        $content = $this->parse_simple_tags($content, $data);
        
        // Parse conditional tags
        $content = $this->parse_conditional_tags($content, $data);
        
        // Parse loop tags
        $content = $this->parse_loop_tags($content, $data);
        
        // Parse date/time tags
        $content = $this->parse_datetime_tags($content);
        
        // Clean up any remaining unparsed tags
        $content = $this->cleanup_unparsed_tags($content);
        
        return $content;
    }
    
    /**
     * Parse simple merge tags like {contact_firstname}
     */
    private function parse_simple_tags($content, $data)
    {
        foreach ($this->merge_tags as $tag => $description) {
            $value = isset($data[$tag]) ? $data[$tag] : '';
            $content = str_replace('{' . $tag . '}', $value, $content);
        }
        
        // Parse any additional data tags not in the predefined list
        foreach ($data as $key => $value) {
            if (is_string($value) || is_numeric($value)) {
                $content = str_replace('{' . $key . '}', $value, $content);
            }
        }
        
        return $content;
    }
    
    /**
     * Parse conditional tags like {if:contact_firstname}Hello {contact_firstname}{/if}
     */
    private function parse_conditional_tags($content, $data)
    {
        // Pattern for conditional tags
        $pattern = '/\{if:([^}]+)\}(.*?)\{\/if\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($data) {
            $condition = $matches[1];
            $conditional_content = $matches[2];
            
            // Check if condition is met
            if ($this->evaluate_condition($condition, $data)) {
                return $this->parse($conditional_content, $data);
            } else {
                return '';
            }
        }, $content);
    }
    
    /**
     * Parse loop tags (for future use with arrays)
     */
    private function parse_loop_tags($content, $data)
    {
        // Pattern for loop tags
        $pattern = '/\{loop:([^}]+)\}(.*?)\{\/loop\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($data) {
            $array_key = $matches[1];
            $loop_content = $matches[2];
            
            if (isset($data[$array_key]) && is_array($data[$array_key])) {
                $output = '';
                foreach ($data[$array_key] as $item) {
                    if (is_array($item)) {
                        $output .= $this->parse($loop_content, $item);
                    }
                }
                return $output;
            }
            
            return '';
        }, $content);
    }
    
    /**
     * Parse date/time tags with formatting
     */
    private function parse_datetime_tags($content)
    {
        // Pattern for date formatting tags like {date:Y-m-d}
        $pattern = '/\{date:([^}]+)\}/';
        
        $content = preg_replace_callback($pattern, function($matches) {
            $format = $matches[1];
            return date($format);
        }, $content);
        
        // Pattern for time formatting tags like {time:H:i:s}
        $pattern = '/\{time:([^}]+)\}/';
        
        $content = preg_replace_callback($pattern, function($matches) {
            $format = $matches[1];
            return date($format);
        }, $content);
        
        return $content;
    }
    
    /**
     * Evaluate condition for conditional tags
     */
    private function evaluate_condition($condition, $data)
    {
        // Simple existence check
        if (isset($data[$condition])) {
            $value = $data[$condition];
            return !empty($value) && $value !== '0' && $value !== 0;
        }
        
        // Check for comparison operators
        if (strpos($condition, '=') !== false) {
            list($key, $expected) = explode('=', $condition, 2);
            $key = trim($key);
            $expected = trim($expected, '"\'');
            
            return isset($data[$key]) && $data[$key] == $expected;
        }
        
        if (strpos($condition, '!=') !== false) {
            list($key, $expected) = explode('!=', $condition, 2);
            $key = trim($key);
            $expected = trim($expected, '"\'');
            
            return !isset($data[$key]) || $data[$key] != $expected;
        }
        
        return false;
    }
    
    /**
     * Clean up any remaining unparsed tags
     */
    private function cleanup_unparsed_tags($content)
    {
        // Remove any remaining merge tags that weren't parsed
        $content = preg_replace('/\{[^}]+\}/', '', $content);
        
        return $content;
    }
    
    /**
     * Get available merge tags
     */
    public function get_available_tags()
    {
        return $this->merge_tags;
    }
    
    /**
     * Validate template syntax
     */
    public function validate_template($content)
    {
        $errors = [];
        $warnings = [];
        
        // Check for unclosed conditional tags
        $if_count = preg_match_all('/\{if:[^}]+\}/', $content);
        $endif_count = preg_match_all('/\{\/if\}/', $content);
        
        if ($if_count != $endif_count) {
            $errors[] = 'Mismatched {if} and {/if} tags';
        }
        
        // Check for unclosed loop tags
        $loop_count = preg_match_all('/\{loop:[^}]+\}/', $content);
        $endloop_count = preg_match_all('/\{\/loop\}/', $content);
        
        if ($loop_count != $endloop_count) {
            $errors[] = 'Mismatched {loop} and {/loop} tags';
        }
        
        // Check for unknown merge tags
        preg_match_all('/\{([^}]+)\}/', $content, $matches);
        
        foreach ($matches[1] as $tag) {
            // Skip conditional and loop tags
            if (strpos($tag, 'if:') === 0 || strpos($tag, '/if') === 0 ||
                strpos($tag, 'loop:') === 0 || strpos($tag, '/loop') === 0 ||
                strpos($tag, 'date:') === 0 || strpos($tag, 'time:') === 0) {
                continue;
            }
            
            if (!isset($this->merge_tags[$tag])) {
                $warnings[] = "Unknown merge tag: {" . $tag . "}";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }
    
    /**
     * Preview template with sample data
     */
    public function preview_template($content, $custom_data = [])
    {
        $sample_data = [
            'contact_firstname' => 'John',
            'contact_lastname' => 'Doe',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567890',
            'company_name' => get_option('companyname') ?: 'Your Company',
            'company_address' => get_option('company_address') ?: '123 Business St',
            'company_phone' => get_option('company_phonenumber') ?: '+1234567890',
            'company_email' => get_option('company_email') ?: '<EMAIL>',
            'company_website' => get_option('company_website') ?: 'https://company.com',
            'current_date' => date('F j, Y'),
            'current_time' => date('g:i A'),
            'month' => date('F'),
            'year' => date('Y'),
            'unsubscribe_url' => '#unsubscribe-link',
            'tracking_pixel' => '',
            'lead_source' => 'Website',
            'lead_status' => 'New Lead',
            'customer_group' => 'VIP Customer',
            'staff_firstname' => 'Jane',
            'staff_lastname' => 'Smith',
            'staff_email' => '<EMAIL>'
        ];
        
        // Merge with custom data
        $data = array_merge($sample_data, $custom_data);
        
        return $this->parse($content, $data);
    }
    
    /**
     * Extract merge tags from content
     */
    public function extract_merge_tags($content)
    {
        preg_match_all('/\{([^}]+)\}/', $content, $matches);
        
        $tags = [];
        foreach ($matches[1] as $tag) {
            // Skip conditional and loop tags
            if (strpos($tag, 'if:') === 0 || strpos($tag, '/if') === 0 ||
                strpos($tag, 'loop:') === 0 || strpos($tag, '/loop') === 0 ||
                strpos($tag, 'date:') === 0 || strpos($tag, 'time:') === 0) {
                continue;
            }
            
            $tags[] = $tag;
        }
        
        return array_unique($tags);
    }
    
    /**
     * Replace merge tags with form inputs for template editor
     */
    public function create_editable_template($content)
    {
        $tags = $this->extract_merge_tags($content);
        
        foreach ($tags as $tag) {
            $description = isset($this->merge_tags[$tag]) ? $this->merge_tags[$tag] : $tag;
            $input = '<span class="merge-tag-input" data-tag="' . $tag . '" title="' . $description . '">{' . $tag . '}</span>';
            $content = str_replace('{' . $tag . '}', $input, $content);
        }
        
        return $content;
    }
    
    /**
     * Generate merge tag suggestions for autocomplete
     */
    public function get_tag_suggestions($partial = '')
    {
        $suggestions = [];
        
        foreach ($this->merge_tags as $tag => $description) {
            if (empty($partial) || strpos($tag, $partial) !== false) {
                $suggestions[] = [
                    'tag' => $tag,
                    'description' => $description,
                    'syntax' => '{' . $tag . '}'
                ];
            }
        }
        
        return $suggestions;
    }
    
    /**
     * Parse template with recipient-specific data
     */
    public function parse_for_recipient($content, $recipient_type, $recipient_id)
    {
        $data = $this->get_recipient_merge_data($recipient_type, $recipient_id);
        return $this->parse($content, $data);
    }
    
    /**
     * Get merge data for specific recipient
     */
    private function get_recipient_merge_data($recipient_type, $recipient_id)
    {
        $data = [
            'company_name' => get_option('companyname'),
            'company_address' => get_option('company_address'),
            'company_phone' => get_option('company_phonenumber'),
            'company_email' => get_option('company_email'),
            'company_website' => get_option('company_website'),
            'current_date' => date('F j, Y'),
            'current_time' => date('g:i A'),
            'month' => date('F'),
            'year' => date('Y')
        ];
        
        switch ($recipient_type) {
            case 'lead':
                $lead_data = $this->get_lead_merge_data($recipient_id);
                $data = array_merge($data, $lead_data);
                break;
                
            case 'customer':
                $customer_data = $this->get_customer_merge_data($recipient_id);
                $data = array_merge($data, $customer_data);
                break;
                
            case 'contact':
                $contact_data = $this->get_contact_merge_data($recipient_id);
                $data = array_merge($data, $contact_data);
                break;
        }
        
        return $data;
    }
    
    /**
     * Get lead merge data
     */
    private function get_lead_merge_data($lead_id)
    {
        $this->CI->db->select('
            l.*,
            ls.name as source_name,
            lst.name as status_name,
            CONCAT(s.firstname, " ", s.lastname) as staff_name,
            s.email as staff_email
        ');
        $this->CI->db->from(db_prefix() . 'leads l');
        $this->CI->db->join(db_prefix() . 'leads_sources ls', 'ls.id = l.source', 'left');
        $this->CI->db->join(db_prefix() . 'leads_status lst', 'lst.id = l.status', 'left');
        $this->CI->db->join(db_prefix() . 'staff s', 's.staffid = l.assigned', 'left');
        $this->CI->db->where('l.id', $lead_id);
        
        $lead = $this->CI->db->get()->row();
        
        if ($lead) {
            return [
                'contact_firstname' => $lead->name,
                'contact_lastname' => $lead->lastname,
                'contact_email' => $lead->email,
                'contact_phone' => $lead->phonenumber,
                'lead_source' => $lead->source_name,
                'lead_status' => $lead->status_name,
                'staff_firstname' => $lead->firstname ?? '',
                'staff_lastname' => $lead->lastname ?? '',
                'staff_email' => $lead->staff_email ?? ''
            ];
        }
        
        return [];
    }
    
    /**
     * Get customer merge data
     */
    private function get_customer_merge_data($customer_id)
    {
        $this->CI->db->select('c.*, cg.name as group_name');
        $this->CI->db->from(db_prefix() . 'clients c');
        $this->CI->db->join(db_prefix() . 'customers_groups cg', 'FIND_IN_SET(cg.id, c.groups_in)', 'left');
        $this->CI->db->where('c.userid', $customer_id);
        
        $customer = $this->CI->db->get()->row();
        
        if ($customer) {
            return [
                'contact_firstname' => $customer->company,
                'contact_lastname' => '',
                'contact_email' => $customer->email,
                'contact_phone' => $customer->phonenumber,
                'customer_group' => $customer->group_name ?? ''
            ];
        }
        
        return [];
    }
    
    /**
     * Get contact merge data
     */
    private function get_contact_merge_data($contact_id)
    {
        $this->CI->db->select('co.*, c.company');
        $this->CI->db->from(db_prefix() . 'contacts co');
        $this->CI->db->join(db_prefix() . 'clients c', 'c.userid = co.userid', 'left');
        $this->CI->db->where('co.id', $contact_id);
        
        $contact = $this->CI->db->get()->row();
        
        if ($contact) {
            return [
                'contact_firstname' => $contact->firstname,
                'contact_lastname' => $contact->lastname,
                'contact_email' => $contact->email,
                'contact_phone' => $contact->phonenumber,
                'company_name' => $contact->company ?? get_option('companyname')
            ];
        }
        
        return [];
    }
}

