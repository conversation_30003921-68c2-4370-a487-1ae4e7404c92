<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Templates Model
 */
class Templates_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_templates';
    }
    
    /**
     * Get all templates
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single template
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Add new template
     */
    public function add($data)
    {
        $data['created_by'] = get_staff_user_id();
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Process settings if provided as array
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            log_activity('New Email Marketing Template Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update template
     */
    public function update($data, $id)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Process settings if provided as array
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = json_encode($data['settings']);
        }
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            log_activity('Email Marketing Template Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete template
     */
    public function delete($id)
    {
        $template = $this->get($id);
        if (!$template) {
            return false;
        }
        
        // Check if template is being used in campaigns
        $this->db->where('template_id', $id);
        $campaigns_using = $this->db->count_all_results(db_prefix() . 'email_marketing_campaigns');
        
        if ($campaigns_using > 0) {
            return ['error' => 'Template is being used in ' . $campaigns_using . ' campaign(s) and cannot be deleted.'];
        }
        
        // Check if template is being used in automation rules
        $this->db->where('template_id', $id);
        $automation_using = $this->db->count_all_results(db_prefix() . 'email_marketing_automation_rules');
        
        if ($automation_using > 0) {
            return ['error' => 'Template is being used in ' . $automation_using . ' automation rule(s) and cannot be deleted.'];
        }
        
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('Email Marketing Template Deleted [ID: ' . $id . ', Name: ' . $template->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Duplicate template
     */
    public function duplicate($id)
    {
        $template = $this->get($id);
        if (!$template) {
            return false;
        }
        
        $new_data = [
            'name' => $template->name . ' (Copy)',
            'subject' => $template->subject,
            'content' => $template->content,
            'template_type' => $template->template_type,
            'is_default' => 0,
            'settings' => $template->settings
        ];
        
        return $this->add($new_data);
    }
    
    /**
     * Get templates by type
     */
    public function get_by_type($type)
    {
        $this->db->where('template_type', $type);
        $this->db->order_by('name');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get default templates
     */
    public function get_default_templates()
    {
        $this->db->where('is_default', 1);
        $this->db->order_by('name');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Set template as default
     */
    public function set_as_default($id, $template_type)
    {
        // First, remove default status from other templates of the same type
        $this->db->where('template_type', $template_type);
        $this->db->update($this->table, ['is_default' => 0]);
        
        // Set the selected template as default
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_default' => 1]);
    }
    
    /**
     * Generate template preview
     */
    public function generate_preview($id, $sample_data = [])
    {
        $template = $this->get($id);
        if (!$template) {
            return '';
        }
        
        $this->load->library('email_marketing/template_parser');
        
        // Default sample data if not provided
        if (empty($sample_data)) {
            $sample_data = [
                'contact_firstname' => 'John',
                'contact_lastname' => 'Doe',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+1234567890',
                'company_name' => get_option('companyname'),
                'company_address' => get_option('company_address'),
                'company_phone' => get_option('company_phonenumber'),
                'company_email' => get_option('company_email'),
                'company_website' => get_option('company_website'),
                'current_date' => date('F j, Y'),
                'current_time' => date('g:i A'),
                'month' => date('F'),
                'year' => date('Y'),
                'unsubscribe_url' => '#unsubscribe',
                'tracking_pixel' => '',
                'lead_source' => 'Website',
                'lead_status' => 'New',
                'customer_group' => 'VIP',
                'staff_firstname' => 'Jane',
                'staff_lastname' => 'Smith',
                'staff_email' => '<EMAIL>'
            ];
        }
        
        $parsed_subject = $this->template_parser->parse($template->subject, $sample_data);
        $parsed_content = $this->template_parser->parse($template->content, $sample_data);
        
        return [
            'subject' => $parsed_subject,
            'content' => $parsed_content
        ];
    }
    
    /**
     * Import template from file
     */
    public function import_template($file_path, $template_data = [])
    {
        if (!file_exists($file_path)) {
            return ['error' => 'Template file not found.'];
        }
        
        $content = file_get_contents($file_path);
        if ($content === false) {
            return ['error' => 'Failed to read template file.'];
        }
        
        // Extract template metadata from HTML comments if present
        $metadata = $this->extract_template_metadata($content);
        
        $data = array_merge([
            'name' => isset($metadata['name']) ? $metadata['name'] : 'Imported Template',
            'subject' => isset($metadata['subject']) ? $metadata['subject'] : 'Imported Template Subject',
            'content' => $content,
            'template_type' => 'campaign',
            'is_default' => 0
        ], $template_data);
        
        $template_id = $this->add($data);
        
        if ($template_id) {
            return ['success' => true, 'template_id' => $template_id];
        } else {
            return ['error' => 'Failed to import template.'];
        }
    }
    
    /**
     * Export template to file
     */
    public function export_template($id)
    {
        $template = $this->get($id);
        if (!$template) {
            return false;
        }
        
        // Add metadata as HTML comments
        $metadata_comment = "<!--\n";
        $metadata_comment .= "Template Name: " . $template->name . "\n";
        $metadata_comment .= "Subject: " . $template->subject . "\n";
        $metadata_comment .= "Type: " . $template->template_type . "\n";
        $metadata_comment .= "Created: " . $template->created_at . "\n";
        $metadata_comment .= "-->\n\n";
        
        $export_content = $metadata_comment . $template->content;
        
        return [
            'filename' => sanitize_filename($template->name) . '.html',
            'content' => $export_content,
            'mime_type' => 'text/html'
        ];
    }
    
    /**
     * Extract template metadata from HTML comments
     */
    private function extract_template_metadata($content)
    {
        $metadata = [];
        
        // Look for metadata in HTML comments
        if (preg_match('/<!--\s*(.*?)\s*-->/s', $content, $matches)) {
            $comment_content = $matches[1];
            $lines = explode("\n", $comment_content);
            
            foreach ($lines as $line) {
                if (strpos($line, ':') !== false) {
                    list($key, $value) = explode(':', $line, 2);
                    $key = strtolower(trim($key));
                    $value = trim($value);
                    
                    switch ($key) {
                        case 'template name':
                        case 'name':
                            $metadata['name'] = $value;
                            break;
                        case 'subject':
                            $metadata['subject'] = $value;
                            break;
                        case 'type':
                            $metadata['type'] = $value;
                            break;
                    }
                }
            }
        }
        
        return $metadata;
    }
    
    /**
     * Get template usage statistics
     */
    public function get_template_usage($id)
    {
        $usage = [];
        
        // Count campaigns using this template
        $this->db->where('template_id', $id);
        $campaigns_count = $this->db->count_all_results(db_prefix() . 'email_marketing_campaigns');
        $usage['campaigns'] = $campaigns_count;
        
        // Count automation rules using this template
        $this->db->where('template_id', $id);
        $automation_count = $this->db->count_all_results(db_prefix() . 'email_marketing_automation_rules');
        $usage['automation_rules'] = $automation_count;
        
        return $usage;
    }
    
    /**
     * Get available merge tags
     */
    public function get_available_merge_tags()
    {
        $CI = &get_instance();
        $CI->load->config('email_marketing/email_marketing_config');
        return $CI->config->item('email_marketing')['merge_tags'];
    }
    
    /**
     * Validate template content
     */
    public function validate_template($content)
    {
        $errors = [];
        $warnings = [];
        
        // Check for basic HTML structure
        if (strpos($content, '<html') === false) {
            $warnings[] = 'Template does not contain HTML structure. Consider adding proper HTML tags.';
        }
        
        // Check for DOCTYPE
        if (strpos($content, '<!DOCTYPE') === false) {
            $warnings[] = 'Template does not contain DOCTYPE declaration.';
        }
        
        // Check for meta viewport (for mobile responsiveness)
        if (strpos($content, 'viewport') === false) {
            $warnings[] = 'Template does not contain viewport meta tag for mobile responsiveness.';
        }
        
        // Check for unsubscribe link
        if (strpos($content, '{unsubscribe_url}') === false && strpos($content, 'unsubscribe') === false) {
            $errors[] = 'Template must contain an unsubscribe link for GDPR compliance.';
        }
        
        // Check for potentially problematic HTML
        $problematic_tags = ['script', 'iframe', 'object', 'embed'];
        foreach ($problematic_tags as $tag) {
            if (strpos($content, '<' . $tag) !== false) {
                $warnings[] = "Template contains <{$tag}> tag which may not be supported by all email clients.";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }
    
    /**
     * Get template categories/types statistics
     */
    public function get_templates_stats()
    {
        $this->db->select('template_type, COUNT(*) as count');
        $this->db->group_by('template_type');
        $by_type = $this->db->get($this->table)->result_array();
        
        $total_templates = $this->db->count_all_results($this->table);
        
        $this->db->where('is_default', 1);
        $default_templates = $this->db->count_all_results($this->table);
        
        return [
            'total_templates' => $total_templates,
            'default_templates' => $default_templates,
            'by_type' => $by_type
        ];
    }
    
    /**
     * Search templates
     */
    public function search_templates($search_term, $filters = [])
    {
        $this->db->like('name', $search_term);
        $this->db->or_like('subject', $search_term);
        $this->db->or_like('content', $search_term);
        
        if (isset($filters['template_type']) && $filters['template_type']) {
            $this->db->where('template_type', $filters['template_type']);
        }
        
        if (isset($filters['is_default']) && $filters['is_default'] !== '') {
            $this->db->where('is_default', $filters['is_default']);
        }
        
        $this->db->order_by('name');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get recently used templates
     */
    public function get_recently_used_templates($limit = 5)
    {
        // This would require tracking template usage
        // For now, return recently created templates
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Clone template with modifications
     */
    public function clone_template($id, $modifications = [])
    {
        $template = $this->get($id);
        if (!$template) {
            return false;
        }
        
        $new_data = [
            'name' => isset($modifications['name']) ? $modifications['name'] : $template->name . ' (Copy)',
            'subject' => isset($modifications['subject']) ? $modifications['subject'] : $template->subject,
            'content' => isset($modifications['content']) ? $modifications['content'] : $template->content,
            'template_type' => isset($modifications['template_type']) ? $modifications['template_type'] : $template->template_type,
            'is_default' => 0,
            'settings' => $template->settings
        ];
        
        return $this->add($new_data);
    }
}

