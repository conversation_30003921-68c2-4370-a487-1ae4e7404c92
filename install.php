<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Module Installation Script
 */

$CI = &get_instance();

// Create database tables
email_marketing_create_tables();

// Insert default settings
email_marketing_insert_default_settings();

// Insert default templates
email_marketing_insert_default_templates();

// Set module as active
add_option('email_marketing_module_active', 1);

/**
 * Create all required database tables
 */
function email_marketing_create_tables()
{
    $CI = &get_instance();
    
    // Email Marketing Campaigns Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_campaigns')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_campaigns` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `subject` varchar(500) NOT NULL,
            `content` longtext NOT NULL,
            `template_id` int(11) DEFAULT NULL,
            `campaign_type` enum(\'email\',\'sms\',\'both\') DEFAULT \'email\',
            `status` enum(\'draft\',\'scheduled\',\'sending\',\'sent\',\'paused\',\'cancelled\') DEFAULT \'draft\',
            `send_date` datetime DEFAULT NULL,
            `created_by` int(11) NOT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `sent_count` int(11) DEFAULT 0,
            `opened_count` int(11) DEFAULT 0,
            `clicked_count` int(11) DEFAULT 0,
            `bounced_count` int(11) DEFAULT 0,
            `unsubscribed_count` int(11) DEFAULT 0,
            `settings` text COMMENT \'JSON settings for campaign\',
            PRIMARY KEY (`id`),
            KEY `idx_status` (`status`),
            KEY `idx_send_date` (`send_date`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Campaign Recipients Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_campaign_recipients')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_campaign_recipients` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `campaign_id` int(11) NOT NULL,
            `recipient_type` enum(\'lead\',\'customer\',\'contact\',\'staff\') NOT NULL,
            `recipient_id` int(11) NOT NULL,
            `email` varchar(255) NOT NULL,
            `phone` varchar(50) DEFAULT NULL,
            `status` enum(\'pending\',\'sent\',\'delivered\',\'opened\',\'clicked\',\'bounced\',\'unsubscribed\',\'failed\') DEFAULT \'pending\',
            `sent_at` datetime DEFAULT NULL,
            `opened_at` datetime DEFAULT NULL,
            `clicked_at` datetime DEFAULT NULL,
            `bounced_at` datetime DEFAULT NULL,
            `unsubscribed_at` datetime DEFAULT NULL,
            `tracking_token` varchar(100) DEFAULT NULL,
            `error_message` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_campaign_recipient` (`campaign_id`, `recipient_type`, `recipient_id`),
            KEY `idx_campaign_id` (`campaign_id`),
            KEY `idx_status` (`status`),
            KEY `idx_tracking_token` (`tracking_token`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Email Templates Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_templates')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `subject` varchar(500) NOT NULL,
            `content` longtext NOT NULL,
            `template_type` enum(\'campaign\',\'automation\',\'system\') DEFAULT \'campaign\',
            `is_default` tinyint(1) DEFAULT 0,
            `created_by` int(11) NOT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `preview_image` varchar(500) DEFAULT NULL,
            `settings` text COMMENT \'JSON settings for template\',
            PRIMARY KEY (`id`),
            KEY `idx_template_type` (`template_type`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Automation Rules Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_automation_rules')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_automation_rules` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `trigger_type` enum(\'lead_created\',\'lead_status_changed\',\'customer_created\',\'custom_date\',\'manual\') NOT NULL,
            `trigger_conditions` text COMMENT \'JSON conditions for trigger\',
            `action_type` enum(\'send_email\',\'send_sms\',\'add_to_campaign\',\'update_status\') NOT NULL,
            `action_settings` text COMMENT \'JSON settings for action\',
            `template_id` int(11) DEFAULT NULL,
            `delay_minutes` int(11) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_by` int(11) NOT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_trigger_type` (`trigger_type`),
            KEY `idx_is_active` (`is_active`),
            KEY `idx_template_id` (`template_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Automation Queue Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_automation_queue')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_automation_queue` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL,
            `recipient_type` enum(\'lead\',\'customer\',\'contact\',\'staff\') NOT NULL,
            `recipient_id` int(11) NOT NULL,
            `email` varchar(255) NOT NULL,
            `phone` varchar(50) DEFAULT NULL,
            `scheduled_at` datetime NOT NULL,
            `status` enum(\'pending\',\'processing\',\'sent\',\'failed\',\'cancelled\') DEFAULT \'pending\',
            `processed_at` datetime DEFAULT NULL,
            `error_message` text DEFAULT NULL,
            `tracking_token` varchar(100) DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_rule_id` (`rule_id`),
            KEY `idx_scheduled_at` (`scheduled_at`),
            KEY `idx_status` (`status`),
            KEY `idx_tracking_token` (`tracking_token`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Segmentation Rules Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_segments')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_segments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `target_type` enum(\'leads\',\'customers\',\'contacts\',\'mixed\') NOT NULL,
            `conditions` text NOT NULL COMMENT \'JSON conditions for segmentation\',
            `created_by` int(11) NOT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `last_calculated` datetime DEFAULT NULL,
            `recipient_count` int(11) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `idx_target_type` (`target_type`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Unsubscribe Management Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_unsubscribes')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_unsubscribes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `email` varchar(255) NOT NULL,
            `recipient_type` enum(\'lead\',\'customer\',\'contact\',\'staff\') DEFAULT NULL,
            `recipient_id` int(11) DEFAULT NULL,
            `unsubscribe_type` enum(\'all\',\'campaigns\',\'automation\') DEFAULT \'all\',
            `reason` varchar(500) DEFAULT NULL,
            `unsubscribed_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `gdpr_compliant` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_email_type` (`email`, `unsubscribe_type`),
            KEY `idx_email` (`email`),
            KEY `idx_recipient` (`recipient_type`, `recipient_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Email Analytics Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_analytics')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_analytics` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `campaign_id` int(11) DEFAULT NULL,
            `automation_rule_id` int(11) DEFAULT NULL,
            `recipient_email` varchar(255) NOT NULL,
            `event_type` enum(\'sent\',\'delivered\',\'opened\',\'clicked\',\'bounced\',\'unsubscribed\',\'complained\') NOT NULL,
            `event_data` text DEFAULT NULL COMMENT \'JSON data for event\',
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `tracking_token` varchar(100) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_campaign_id` (`campaign_id`),
            KEY `idx_automation_rule_id` (`automation_rule_id`),
            KEY `idx_event_type` (`event_type`),
            KEY `idx_tracking_token` (`tracking_token`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // SMTP Configuration Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_smtp_configs')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_smtp_configs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `smtp_host` varchar(255) NOT NULL,
            `smtp_port` int(11) NOT NULL DEFAULT 587,
            `smtp_username` varchar(255) NOT NULL,
            `smtp_password` text NOT NULL,
            `smtp_encryption` enum(\'none\',\'ssl\',\'tls\') DEFAULT \'tls\',
            `from_email` varchar(255) NOT NULL,
            `from_name` varchar(255) NOT NULL,
            `reply_to_email` varchar(255) DEFAULT NULL,
            `is_default` tinyint(1) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `daily_limit` int(11) DEFAULT 0,
            `hourly_limit` int(11) DEFAULT 0,
            `sent_today` int(11) DEFAULT 0,
            `sent_this_hour` int(11) DEFAULT 0,
            `last_reset_date` date DEFAULT NULL,
            `last_reset_hour` datetime DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_is_default` (`is_default`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
    
    // Module Settings Table
    if (!$CI->db->table_exists(db_prefix() . 'email_marketing_settings')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . 'email_marketing_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(255) NOT NULL,
            `setting_value` longtext DEFAULT NULL,
            `setting_type` enum(\'string\',\'integer\',\'boolean\',\'json\',\'text\') DEFAULT \'string\',
            `description` text DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
    }
}

/**
 * Insert default module settings
 */
function email_marketing_insert_default_settings()
{
    $default_settings = [
        [
            'setting_key' => 'email_marketing_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable/disable email marketing module'
        ],
        [
            'setting_key' => 'email_marketing_default_from_name',
            'setting_value' => get_option('companyname'),
            'setting_type' => 'string',
            'description' => 'Default from name for emails'
        ],
        [
            'setting_key' => 'email_marketing_default_from_email',
            'setting_value' => get_option('smtp_email'),
            'setting_type' => 'string',
            'description' => 'Default from email address'
        ],
        [
            'setting_key' => 'email_marketing_tracking_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable email tracking (opens, clicks)'
        ],
        [
            'setting_key' => 'email_marketing_gdpr_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable GDPR compliance features'
        ],
        [
            'setting_key' => 'email_marketing_unsubscribe_page',
            'setting_value' => '',
            'setting_type' => 'string',
            'description' => 'Custom unsubscribe page URL'
        ],
        [
            'setting_key' => 'email_marketing_batch_size',
            'setting_value' => '50',
            'setting_type' => 'integer',
            'description' => 'Number of emails to send per batch'
        ],
        [
            'setting_key' => 'email_marketing_batch_delay',
            'setting_value' => '30',
            'setting_type' => 'integer',
            'description' => 'Delay between batches in seconds'
        ]
    ];
    
    $CI = &get_instance();
    
    foreach ($default_settings as $setting) {
        $CI->db->insert(db_prefix() . 'email_marketing_settings', $setting);
    }
}

/**
 * Insert default email templates
 */
function email_marketing_insert_default_templates()
{
    $default_templates = [
        [
            'name' => 'Welcome Email',
            'subject' => 'Welcome to {company_name}!',
            'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Email</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2c3e50;">Welcome to {company_name}!</h1>
        <p>Dear {contact_firstname},</p>
        <p>Thank you for your interest in our services. We are excited to have you as part of our community.</p>
        <p>If you have any questions, please don\'t hesitate to contact us.</p>
        <p>Best regards,<br>{company_name} Team</p>
        <hr style="margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            If you no longer wish to receive these emails, you can <a href="{unsubscribe_url}">unsubscribe here</a>.
        </p>
    </div>
</body>
</html>',
            'template_type' => 'automation',
            'is_default' => 1,
            'created_by' => 1
        ],
        [
            'name' => 'Newsletter Template',
            'subject' => '{company_name} Newsletter - {month} {year}',
            'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2c3e50;">{company_name} Newsletter</h1>
        <p>Dear {contact_firstname},</p>
        <p>Here are the latest updates from {company_name}:</p>
        
        <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Latest News</h2>
            <p>Add your newsletter content here...</p>
        </div>
        
        <p>Thank you for being a valued member of our community.</p>
        <p>Best regards,<br>{company_name} Team</p>
        <hr style="margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            If you no longer wish to receive these emails, you can <a href="{unsubscribe_url}">unsubscribe here</a>.
        </p>
    </div>
</body>
</html>',
            'template_type' => 'campaign',
            'is_default' => 1,
            'created_by' => 1
        ]
    ];
    
    $CI = &get_instance();
    
    foreach ($default_templates as $template) {
        $CI->db->insert(db_prefix() . 'email_marketing_templates', $template);
    }
}

