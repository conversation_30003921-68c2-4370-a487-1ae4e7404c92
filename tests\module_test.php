<?php

/**
 * Email Marketing Module Test Suite
 * Comprehensive testing for all module functionality
 */

defined('BASEPATH') or exit('No direct script access allowed');

class Email_marketing_test
{
    private $CI;
    private $test_results = [];
    private $test_data = [];
    
    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->database();
        
        // Load all email marketing models
        $this->CI->load->model('campaigns_model');
        $this->CI->load->model('templates_model');
        $this->CI->load->model('segments_model');
        $this->CI->load->model('automation_model');
        $this->CI->load->model('lead_response_model');
        $this->CI->load->model('unsubscribe_model');
        $this->CI->load->model('smtp_model');

        // Load libraries
        $this->CI->load->library('email_sender');
        $this->CI->load->library('template_parser');
    }
    
    /**
     * Run all tests
     */
    public function run_all_tests()
    {
        echo "<h1>Email Marketing Module Test Suite</h1>\n";
        echo "<style>
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            .test-result { margin: 5px 0; padding: 5px; }
        </style>\n";
        
        // Setup test data
        $this->setup_test_data();
        
        // Run test suites
        $this->test_database_operations();
        $this->test_template_functionality();
        $this->test_segment_functionality();
        $this->test_campaign_functionality();
        $this->test_automation_functionality();
        $this->test_lead_response_functionality();
        $this->test_email_sending();
        $this->test_unsubscribe_functionality();
        $this->test_analytics_functionality();
        $this->test_api_endpoints();
        
        // Cleanup test data
        $this->cleanup_test_data();
        
        // Display summary
        $this->display_test_summary();
    }
    
    /**
     * Setup test data
     */
    private function setup_test_data()
    {
        echo "<div class='test-section'><h2>Setting up test data...</h2>\n";
        
        // Create test SMTP configuration
        $smtp_data = [
            'name' => 'Test SMTP',
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '<EMAIL>',
            'password' => 'test_password',
            'encryption' => 'tls',
            'from_name' => 'Test Sender',
            'from_email' => '<EMAIL>',
            'is_active' => 1,
            'daily_limit' => 1000,
            'hourly_limit' => 100
        ];
        
        $this->test_data['smtp_id'] = $this->CI->smtp_model->add($smtp_data);
        $this->log_test('SMTP Configuration Created', $this->test_data['smtp_id'] > 0);
        
        // Create test template
        $template_data = [
            'name' => 'Test Template',
            'subject' => 'Test Email Subject - {contact_name}',
            'content' => '<h1>Hello {contact_name}!</h1><p>This is a test email from {company_name}.</p>',
            'type' => 'email',
            'category' => 'test',
            'is_active' => 1
        ];
        
        $this->test_data['template_id'] = $this->CI->templates_model->add($template_data);
        $this->log_test('Email Template Created', $this->test_data['template_id'] > 0);
        
        // Create test segment
        $segment_data = [
            'name' => 'Test Segment',
            'description' => 'Test segment for module testing',
            'conditions' => json_encode([
                'lead_sources' => ['website'],
                'lead_statuses' => ['new'],
                'countries' => ['US']
            ]),
            'recipient_type' => 'leads',
            'is_active' => 1
        ];
        
        $this->test_data['segment_id'] = $this->CI->segments_model->add($segment_data);
        $this->log_test('Segment Created', $this->test_data['segment_id'] > 0);
        
        echo "</div>\n";
    }
    
    /**
     * Test database operations
     */
    private function test_database_operations()
    {
        echo "<div class='test-section'><h2>Testing Database Operations</h2>\n";
        
        // Test table existence
        $required_tables = [
            'email_marketing_campaigns',
            'email_marketing_templates',
            'email_marketing_segments',
            'email_marketing_automation_rules',
            'email_marketing_lead_response_rules',
            'email_marketing_unsubscribes',
            'email_marketing_smtp_configs',
            'email_marketing_analytics'
        ];
        
        foreach ($required_tables as $table) {
            $exists = $this->CI->db->table_exists(db_prefix() . $table);
            $this->log_test("Table {$table} exists", $exists);
        }
        
        // Test database connections
        $db_connected = $this->CI->db->initialize();
        $this->log_test('Database Connection', $db_connected);
        
        echo "</div>\n";
    }
    
    /**
     * Test template functionality
     */
    private function test_template_functionality()
    {
        echo "<div class='test-section'><h2>Testing Template Functionality</h2>\n";
        
        // Test template retrieval
        $template = $this->CI->templates_model->get($this->test_data['template_id']);
        $this->log_test('Template Retrieval', !empty($template));
        
        // Test template parsing
        $test_data = [
            'contact_name' => 'John Doe',
            'company_name' => 'Test Company'
        ];
        
        $parsed_subject = $this->CI->template_parser->parse_content($template->subject, $test_data);
        $parsed_content = $this->CI->template_parser->parse_content($template->content, $test_data);
        
        $this->log_test('Template Subject Parsing', strpos($parsed_subject, 'John Doe') !== false);
        $this->log_test('Template Content Parsing', strpos($parsed_content, 'John Doe') !== false);
        
        // Test template validation
        $validation_result = $this->CI->templates_model->validate_template($template->content);
        $this->log_test('Template Validation', $validation_result['valid']);
        
        echo "</div>\n";
    }
    
    /**
     * Test segment functionality
     */
    private function test_segment_functionality()
    {
        echo "<div class='test-section'><h2>Testing Segment Functionality</h2>\n";
        
        // Test segment retrieval
        $segment = $this->CI->segments_model->get($this->test_data['segment_id']);
        $this->log_test('Segment Retrieval', !empty($segment));
        
        // Test recipient calculation
        $recipients = $this->CI->segments_model->get_recipients($this->test_data['segment_id']);
        $this->log_test('Recipient Calculation', is_array($recipients));
        
        // Test segment conditions parsing
        $conditions = $this->CI->segments_model->parse_conditions($segment->conditions);
        $this->log_test('Conditions Parsing', is_array($conditions));
        
        echo "</div>\n";
    }
    
    /**
     * Test campaign functionality
     */
    private function test_campaign_functionality()
    {
        echo "<div class='test-section'><h2>Testing Campaign Functionality</h2>\n";
        
        // Create test campaign
        $campaign_data = [
            'name' => 'Test Campaign',
            'subject' => 'Test Campaign Subject',
            'content' => '<h1>Test Campaign</h1><p>This is a test campaign.</p>',
            'type' => 'email',
            'template_id' => $this->test_data['template_id'],
            'segment_id' => $this->test_data['segment_id'],
            'status' => 'draft',
            'send_immediately' => 0
        ];
        
        $campaign_id = $this->CI->campaigns_model->add($campaign_data);
        $this->test_data['campaign_id'] = $campaign_id;
        $this->log_test('Campaign Creation', $campaign_id > 0);
        
        // Test campaign retrieval
        $campaign = $this->CI->campaigns_model->get($campaign_id);
        $this->log_test('Campaign Retrieval', !empty($campaign));
        
        // Test campaign validation
        $validation = $this->CI->campaigns_model->validate_campaign($campaign_id);
        $this->log_test('Campaign Validation', $validation['valid']);
        
        // Test campaign preview
        $preview = $this->CI->campaigns_model->generate_preview($campaign_id);
        $this->log_test('Campaign Preview Generation', !empty($preview));
        
        echo "</div>\n";
    }
    
    /**
     * Test automation functionality
     */
    private function test_automation_functionality()
    {
        echo "<div class='test-section'><h2>Testing Automation Functionality</h2>\n";
        
        // Create test automation rule
        $automation_data = [
            'name' => 'Test Automation',
            'description' => 'Test automation rule',
            'trigger_type' => 'lead_created',
            'conditions' => json_encode(['lead_sources' => ['website']]),
            'actions' => json_encode([
                [
                    'type' => 'send_email',
                    'template_id' => $this->test_data['template_id'],
                    'delay' => 0
                ]
            ]),
            'is_active' => 1
        ];
        
        $automation_id = $this->CI->automation_model->add($automation_data);
        $this->test_data['automation_id'] = $automation_id;
        $this->log_test('Automation Rule Creation', $automation_id > 0);
        
        // Test automation rule retrieval
        $automation = $this->CI->automation_model->get($automation_id);
        $this->log_test('Automation Rule Retrieval', !empty($automation));
        
        // Test condition matching
        $test_lead_data = ['source' => 'website', 'status' => 'new'];
        $matches = $this->CI->automation_model->check_conditions($automation->conditions, $test_lead_data);
        $this->log_test('Automation Condition Matching', $matches);
        
        // Test queue processing
        $queue_processed = $this->CI->automation_model->process_queue(1);
        $this->log_test('Automation Queue Processing', is_numeric($queue_processed));
        
        echo "</div>\n";
    }
    
    /**
     * Test lead response functionality
     */
    private function test_lead_response_functionality()
    {
        echo "<div class='test-section'><h2>Testing Lead Response Functionality</h2>\n";
        
        // Create test lead response rule
        $lead_response_data = [
            'name' => 'Test Lead Response',
            'description' => 'Test lead response rule',
            'trigger_conditions' => json_encode(['lead_sources' => ['website']]),
            'template_id' => $this->test_data['template_id'],
            'delay_minutes' => 0,
            'is_active' => 1,
            'send_once_per_lead' => 1
        ];
        
        $lead_response_id = $this->CI->lead_response_model->add($lead_response_data);
        $this->test_data['lead_response_id'] = $lead_response_id;
        $this->log_test('Lead Response Rule Creation', $lead_response_id > 0);
        
        // Test lead response rule retrieval
        $lead_response = $this->CI->lead_response_model->get($lead_response_id);
        $this->log_test('Lead Response Rule Retrieval', !empty($lead_response));
        
        // Test lead processing simulation
        $test_lead_id = 1; // Assuming lead ID 1 exists
        $responses_sent = $this->CI->lead_response_model->process_new_lead($test_lead_id);
        $this->log_test('Lead Response Processing', is_numeric($responses_sent));
        
        echo "</div>\n";
    }
    
    /**
     * Test email sending functionality
     */
    private function test_email_sending()
    {
        echo "<div class='test-section'><h2>Testing Email Sending Functionality</h2>\n";
        
        // Test SMTP configuration
        $smtp_config = $this->CI->smtp_model->get($this->test_data['smtp_id']);
        $this->log_test('SMTP Configuration Retrieval', !empty($smtp_config));
        
        // Test email sender initialization
        $email_sender_initialized = $this->CI->email_sender->initialize($smtp_config);
        $this->log_test('Email Sender Initialization', $email_sender_initialized);
        
        // Test email composition
        $email_data = [
            'to' => '<EMAIL>',
            'subject' => 'Test Email',
            'content' => '<h1>Test Email</h1><p>This is a test email.</p>',
            'tracking_token' => 'test_token_' . time()
        ];
        
        $email_composed = $this->CI->email_sender->compose_email($email_data);
        $this->log_test('Email Composition', $email_composed);
        
        // Test tracking link generation
        $tracking_links = $this->CI->email_sender->add_tracking_links($email_data['content'], $email_data['tracking_token']);
        $this->log_test('Tracking Links Generation', !empty($tracking_links));
        
        echo "</div>\n";
    }
    
    /**
     * Test unsubscribe functionality
     */
    private function test_unsubscribe_functionality()
    {
        echo "<div class='test-section'><h2>Testing Unsubscribe Functionality</h2>\n";
        
        // Test unsubscribe token validation
        $test_token = 'test_token_' . time();
        $token_valid = $this->CI->unsubscribe_model->validate_token($test_token);
        $this->log_test('Unsubscribe Token Validation', is_bool($token_valid));
        
        // Test email preferences
        $test_email = '<EMAIL>';
        $preferences = $this->CI->unsubscribe_model->get_email_preferences($test_email);
        $this->log_test('Email Preferences Retrieval', is_array($preferences));
        
        // Test preference updates
        $new_preferences = [
            'marketing_emails' => 0,
            'newsletter' => 1,
            'promotions' => 0
        ];
        $preferences_updated = $this->CI->unsubscribe_model->update_preferences($test_email, $new_preferences);
        $this->log_test('Email Preferences Update', $preferences_updated);
        
        // Test suppression list
        $suppression_list = $this->CI->unsubscribe_model->get_suppression_list('marketing');
        $this->log_test('Suppression List Generation', is_array($suppression_list));
        
        echo "</div>\n";
    }
    
    /**
     * Test analytics functionality
     */
    private function test_analytics_functionality()
    {
        echo "<div class='test-section'><h2>Testing Analytics Functionality</h2>\n";
        
        // Load analytics model
        $this->CI->load->model('analytics_model');
        
        // Test analytics data recording
        $analytics_data = [
            'tracking_token' => 'test_token_' . time(),
            'event_type' => 'opened',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test User Agent',
            'event_time' => date('Y-m-d H:i:s')
        ];
        
        $analytics_recorded = $this->CI->analytics_model->log_event($analytics_data);
        $this->log_test('Analytics Event Recording', $analytics_recorded);
        
        // Test analytics retrieval
        $campaign_stats = $this->CI->analytics_model->get_campaign_stats($this->test_data['campaign_id']);
        $this->log_test('Campaign Statistics Retrieval', is_array($campaign_stats));
        
        // Test performance metrics
        $performance_data = $this->CI->analytics_model->get_performance_data(30);
        $this->log_test('Performance Data Retrieval', is_array($performance_data));
        
        echo "</div>\n";
    }
    
    /**
     * Test API endpoints
     */
    private function test_api_endpoints()
    {
        echo "<div class='test-section'><h2>Testing API Endpoints</h2>\n";
        
        // Test campaign API endpoints
        $campaign_data = $this->CI->campaigns_model->get_all();
        $this->log_test('Campaign API Data Retrieval', is_array($campaign_data));
        
        // Test template API endpoints
        $template_data = $this->CI->templates_model->get_all();
        $this->log_test('Template API Data Retrieval', is_array($template_data));
        
        // Test segment API endpoints
        $segment_data = $this->CI->segments_model->get_all();
        $this->log_test('Segment API Data Retrieval', is_array($segment_data));
        
        echo "</div>\n";
    }
    
    /**
     * Cleanup test data
     */
    private function cleanup_test_data()
    {
        echo "<div class='test-section'><h2>Cleaning up test data...</h2>\n";
        
        // Delete test campaign
        if (isset($this->test_data['campaign_id'])) {
            $campaign_deleted = $this->CI->campaigns_model->delete($this->test_data['campaign_id']);
            $this->log_test('Test Campaign Cleanup', $campaign_deleted);
        }
        
        // Delete test automation
        if (isset($this->test_data['automation_id'])) {
            $automation_deleted = $this->CI->automation_model->delete($this->test_data['automation_id']);
            $this->log_test('Test Automation Cleanup', $automation_deleted);
        }
        
        // Delete test lead response
        if (isset($this->test_data['lead_response_id'])) {
            $lead_response_deleted = $this->CI->lead_response_model->delete($this->test_data['lead_response_id']);
            $this->log_test('Test Lead Response Cleanup', $lead_response_deleted);
        }
        
        // Delete test segment
        if (isset($this->test_data['segment_id'])) {
            $segment_deleted = $this->CI->segments_model->delete($this->test_data['segment_id']);
            $this->log_test('Test Segment Cleanup', $segment_deleted);
        }
        
        // Delete test template
        if (isset($this->test_data['template_id'])) {
            $template_deleted = $this->CI->templates_model->delete($this->test_data['template_id']);
            $this->log_test('Test Template Cleanup', $template_deleted);
        }
        
        // Delete test SMTP config
        if (isset($this->test_data['smtp_id'])) {
            $smtp_deleted = $this->CI->smtp_model->delete($this->test_data['smtp_id']);
            $this->log_test('Test SMTP Config Cleanup', $smtp_deleted);
        }
        
        echo "</div>\n";
    }
    
    /**
     * Log test result
     */
    private function log_test($test_name, $passed, $message = '')
    {
        $this->test_results[] = [
            'name' => $test_name,
            'passed' => $passed,
            'message' => $message
        ];
        
        $status_class = $passed ? 'test-pass' : 'test-fail';
        $status_text = $passed ? 'PASS' : 'FAIL';
        
        echo "<div class='test-result'>";
        echo "<span class='{$status_class}'>[{$status_text}]</span> {$test_name}";
        if ($message) {
            echo " - {$message}";
        }
        echo "</div>\n";
    }
    
    /**
     * Display test summary
     */
    private function display_test_summary()
    {
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($test) {
            return $test['passed'];
        }));
        $failed_tests = $total_tests - $passed_tests;
        
        echo "<div class='test-section'>";
        echo "<h2>Test Summary</h2>";
        echo "<p><strong>Total Tests:</strong> {$total_tests}</p>";
        echo "<p><strong class='test-pass'>Passed:</strong> {$passed_tests}</p>";
        echo "<p><strong class='test-fail'>Failed:</strong> {$failed_tests}</p>";
        
        $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
        echo "<p><strong>Success Rate:</strong> {$success_rate}%</p>";
        
        if ($failed_tests > 0) {
            echo "<h3>Failed Tests:</h3>";
            echo "<ul>";
            foreach ($this->test_results as $test) {
                if (!$test['passed']) {
                    echo "<li class='test-fail'>{$test['name']}";
                    if ($test['message']) {
                        echo " - {$test['message']}";
                    }
                    echo "</li>";
                }
            }
            echo "</ul>";
        }
        
        echo "</div>\n";
        
        // Overall result
        if ($success_rate >= 90) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h2>✅ Module Testing Completed Successfully!</h2>";
            echo "<p>The Email Marketing module has passed {$success_rate}% of tests and is ready for production use.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h2>❌ Module Testing Issues Detected</h2>";
            echo "<p>The Email Marketing module has some issues that need to be addressed before production use.</p>";
            echo "</div>";
        }
    }
}

// Run tests if accessed directly
if (isset($_GET['run_tests']) && $_GET['run_tests'] == '1') {
    $test_suite = new Email_marketing_test();
    $test_suite->run_all_tests();
}

