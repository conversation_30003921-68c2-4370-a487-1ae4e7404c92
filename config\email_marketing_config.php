<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Module Configuration
 */

// Module configuration settings
$config['email_marketing'] = [
    'version' => '1.0.0',
    'name' => 'Email Marketing & Automation',
    'description' => 'Comprehensive email marketing and automation module for Perfex CRM',
    'author' => 'Manus AI',
    'author_uri' => 'https://manus.ai',
    'requires_at_least' => '2.3.0',
    'tested_up_to' => '3.0.0',
    
    // Email settings
    'default_batch_size' => 50,
    'default_batch_delay' => 30, // seconds
    'max_daily_emails' => 10000,
    'max_hourly_emails' => 1000,
    
    // Tracking settings
    'tracking_pixel_enabled' => true,
    'click_tracking_enabled' => true,
    'bounce_tracking_enabled' => true,
    
    // GDPR settings
    'gdpr_compliance_enabled' => true,
    'consent_required' => true,
    'data_retention_days' => 365,
    
    // Automation settings
    'automation_enabled' => true,
    'queue_processing_enabled' => true,
    'max_automation_delay' => 30, // days
    
    // Template settings
    'allow_html_templates' => true,
    'template_cache_enabled' => true,
    'merge_tags_enabled' => true,
    
    // Security settings
    'csrf_protection' => true,
    'xss_protection' => true,
    'sql_injection_protection' => true,
    
    // API settings
    'api_enabled' => true,
    'api_rate_limit' => 1000, // requests per hour
    'webhook_enabled' => true,
    
    // Supported merge tags
    'merge_tags' => [
        'contact_firstname' => 'Contact First Name',
        'contact_lastname' => 'Contact Last Name',
        'contact_email' => 'Contact Email',
        'contact_phone' => 'Contact Phone',
        'company_name' => 'Company Name',
        'company_address' => 'Company Address',
        'company_phone' => 'Company Phone',
        'company_email' => 'Company Email',
        'company_website' => 'Company Website',
        'current_date' => 'Current Date',
        'current_time' => 'Current Time',
        'unsubscribe_url' => 'Unsubscribe URL',
        'tracking_pixel' => 'Tracking Pixel',
        'month' => 'Current Month',
        'year' => 'Current Year',
        'lead_source' => 'Lead Source',
        'lead_status' => 'Lead Status',
        'customer_group' => 'Customer Group',
        'staff_firstname' => 'Assigned Staff First Name',
        'staff_lastname' => 'Assigned Staff Last Name',
        'staff_email' => 'Assigned Staff Email'
    ],
    
    // Email templates directory
    'templates_path' => 'modules/email_marketing/views/emails/',
    
    // Assets configuration
    'assets' => [
        'css' => [
            'email_marketing.css',
            'campaign_builder.css',
            'template_editor.css'
        ],
        'js' => [
            'email_marketing.js',
            'campaign_builder.js',
            'template_editor.js',
            'analytics.js'
        ]
    ],
    
    // Database table names (without prefix)
    'tables' => [
        'campaigns' => 'email_marketing_campaigns',
        'campaign_recipients' => 'email_marketing_campaign_recipients',
        'templates' => 'email_marketing_templates',
        'automation_rules' => 'email_marketing_automation_rules',
        'automation_queue' => 'email_marketing_automation_queue',
        'segments' => 'email_marketing_segments',
        'unsubscribes' => 'email_marketing_unsubscribes',
        'analytics' => 'email_marketing_analytics',
        'smtp_configs' => 'email_marketing_smtp_configs',
        'settings' => 'email_marketing_settings'
    ],
    
    // Supported campaign types
    'campaign_types' => [
        'email' => 'Email Campaign',
        'sms' => 'SMS Campaign',
        'both' => 'Email & SMS Campaign'
    ],
    
    // Campaign statuses
    'campaign_statuses' => [
        'draft' => 'Draft',
        'scheduled' => 'Scheduled',
        'sending' => 'Sending',
        'sent' => 'Sent',
        'paused' => 'Paused',
        'cancelled' => 'Cancelled'
    ],
    
    // Automation trigger types
    'automation_triggers' => [
        'lead_created' => 'Lead Created',
        'lead_status_changed' => 'Lead Status Changed',
        'customer_created' => 'Customer Created',
        'custom_date' => 'Custom Date',
        'manual' => 'Manual Trigger'
    ],
    
    // Automation action types
    'automation_actions' => [
        'send_email' => 'Send Email',
        'send_sms' => 'Send SMS',
        'add_to_campaign' => 'Add to Campaign',
        'update_status' => 'Update Status'
    ],
    
    // Segmentation target types
    'segment_targets' => [
        'leads' => 'Leads Only',
        'customers' => 'Customers Only',
        'contacts' => 'Contacts Only',
        'mixed' => 'Mixed (Leads & Customers)'
    ],
    
    // SMTP encryption types
    'smtp_encryption' => [
        'none' => 'None',
        'ssl' => 'SSL',
        'tls' => 'TLS'
    ],
    
    // Analytics event types
    'analytics_events' => [
        'sent' => 'Email Sent',
        'delivered' => 'Email Delivered',
        'opened' => 'Email Opened',
        'clicked' => 'Link Clicked',
        'bounced' => 'Email Bounced',
        'unsubscribed' => 'Unsubscribed',
        'complained' => 'Spam Complaint'
    ],
    
    // Default SMTP settings
    'default_smtp' => [
        'host' => 'localhost',
        'port' => 587,
        'encryption' => 'tls',
        'timeout' => 30
    ],
    
    // Cron job settings
    'cron' => [
        'automation_queue_interval' => 300, // 5 minutes
        'analytics_cleanup_interval' => 86400, // 24 hours
        'bounce_processing_interval' => 1800, // 30 minutes
        'rate_limit_reset_interval' => 3600 // 1 hour
    ]
];

