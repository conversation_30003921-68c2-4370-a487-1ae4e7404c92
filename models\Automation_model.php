<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Automation Model
 */
class Automation_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_automation_rules';
        $this->queue_table = db_prefix() . 'email_marketing_automation_queue';
    }
    
    /**
     * Get all automation rules
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }

        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single automation rule
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Add new automation rule
     */
    public function add($data)
    {
        $data['created_by'] = get_staff_user_id();
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Process conditions and settings if provided as arrays
        if (isset($data['trigger_conditions']) && is_array($data['trigger_conditions'])) {
            $data['trigger_conditions'] = json_encode($data['trigger_conditions']);
        }
        
        if (isset($data['action_settings']) && is_array($data['action_settings'])) {
            $data['action_settings'] = json_encode($data['action_settings']);
        }
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            log_activity('New Email Marketing Automation Rule Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update automation rule
     */
    public function update($data, $id)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Process conditions and settings if provided as arrays
        if (isset($data['trigger_conditions']) && is_array($data['trigger_conditions'])) {
            $data['trigger_conditions'] = json_encode($data['trigger_conditions']);
        }
        
        if (isset($data['action_settings']) && is_array($data['action_settings'])) {
            $data['action_settings'] = json_encode($data['action_settings']);
        }
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            log_activity('Email Marketing Automation Rule Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete automation rule
     */
    public function delete($id)
    {
        $rule = $this->get($id);
        if (!$rule) {
            return false;
        }
        
        // Delete related queue items
        $this->db->where('rule_id', $id);
        $this->db->delete($this->queue_table);
        
        // Delete analytics data
        $this->db->where('automation_rule_id', $id);
        $this->db->delete(db_prefix() . 'email_marketing_analytics');
        
        // Delete rule
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('Email Marketing Automation Rule Deleted [ID: ' . $id . ', Name: ' . $rule->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Activate automation rule
     */
    public function activate($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_active' => 1]);
    }
    
    /**
     * Deactivate automation rule
     */
    public function deactivate($id)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, ['is_active' => 0]);
    }
    
    /**
     * Process trigger for automation rules
     */
    public function process_trigger($trigger_type, $entity_id, $additional_data = [])
    {
        // Get active automation rules for this trigger type
        $this->db->where('trigger_type', $trigger_type);
        $this->db->where('is_active', 1);
        $rules = $this->db->get($this->table)->result();
        
        foreach ($rules as $rule) {
            if ($this->check_trigger_conditions($rule, $entity_id, $additional_data)) {
                $this->queue_automation_action($rule, $entity_id, $additional_data);
            }
        }
    }
    
    /**
     * Check if trigger conditions are met
     */
    private function check_trigger_conditions($rule, $entity_id, $additional_data)
    {
        $conditions = json_decode($rule->trigger_conditions, true);
        
        if (empty($conditions)) {
            return true; // No conditions means always trigger
        }
        
        switch ($rule->trigger_type) {
            case 'lead_created':
                return $this->check_lead_conditions($entity_id, $conditions);
                
            case 'lead_status_changed':
                return $this->check_lead_status_conditions($entity_id, $conditions, $additional_data);
                
            case 'customer_created':
                return $this->check_customer_conditions($entity_id, $conditions);
                
            case 'custom_date':
                return $this->check_date_conditions($conditions);
                
            default:
                return true;
        }
    }
    
    /**
     * Check lead conditions
     */
    private function check_lead_conditions($lead_id, $conditions)
    {
        $this->db->where('id', $lead_id);
        $lead = $this->db->get(db_prefix() . 'leads')->row();
        
        if (!$lead) {
            return false;
        }
        
        // Check source condition
        if (isset($conditions['sources']) && !empty($conditions['sources'])) {
            if (!in_array($lead->source, $conditions['sources'])) {
                return false;
            }
        }
        
        // Check status condition
        if (isset($conditions['statuses']) && !empty($conditions['statuses'])) {
            if (!in_array($lead->status, $conditions['statuses'])) {
                return false;
            }
        }
        
        // Check assigned staff condition
        if (isset($conditions['assigned_staff']) && !empty($conditions['assigned_staff'])) {
            if (!in_array($lead->assigned, $conditions['assigned_staff'])) {
                return false;
            }
        }
        
        // Check country condition
        if (isset($conditions['countries']) && !empty($conditions['countries'])) {
            if (!in_array($lead->country, $conditions['countries'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check lead status change conditions
     */
    private function check_lead_status_conditions($lead_id, $conditions, $additional_data)
    {
        // Check if the new status matches the condition
        if (isset($conditions['new_status']) && !empty($conditions['new_status'])) {
            $new_status = isset($additional_data['new_status']) ? $additional_data['new_status'] : null;
            if (!in_array($new_status, $conditions['new_status'])) {
                return false;
            }
        }
        
        // Check if the old status matches the condition
        if (isset($conditions['old_status']) && !empty($conditions['old_status'])) {
            $old_status = isset($additional_data['old_status']) ? $additional_data['old_status'] : null;
            if (!in_array($old_status, $conditions['old_status'])) {
                return false;
            }
        }
        
        // Also check general lead conditions
        return $this->check_lead_conditions($lead_id, $conditions);
    }
    
    /**
     * Check customer conditions
     */
    private function check_customer_conditions($customer_id, $conditions)
    {
        $this->db->where('userid', $customer_id);
        $customer = $this->db->get(db_prefix() . 'clients')->row();
        
        if (!$customer) {
            return false;
        }
        
        // Check customer group condition
        if (isset($conditions['groups']) && !empty($conditions['groups'])) {
            $customer_groups = explode(',', $customer->groups_in);
            $has_matching_group = false;
            
            foreach ($conditions['groups'] as $group_id) {
                if (in_array($group_id, $customer_groups)) {
                    $has_matching_group = true;
                    break;
                }
            }
            
            if (!$has_matching_group) {
                return false;
            }
        }
        
        // Check country condition
        if (isset($conditions['countries']) && !empty($conditions['countries'])) {
            if (!in_array($customer->country, $conditions['countries'])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check date conditions
     */
    private function check_date_conditions($conditions)
    {
        if (isset($conditions['date']) && isset($conditions['time'])) {
            $trigger_datetime = $conditions['date'] . ' ' . $conditions['time'];
            $current_datetime = date('Y-m-d H:i:s');
            
            return $current_datetime >= $trigger_datetime;
        }
        
        return false;
    }
    
    /**
     * Queue automation action
     */
    private function queue_automation_action($rule, $entity_id, $additional_data)
    {
        // Get recipient information based on trigger type
        $recipient = $this->get_recipient_for_trigger($rule->trigger_type, $entity_id);
        
        if (!$recipient) {
            return false;
        }
        
        // Calculate scheduled time (current time + delay)
        $delay_minutes = $rule->delay_minutes ?: 0;
        $scheduled_at = date('Y-m-d H:i:s', strtotime('+' . $delay_minutes . ' minutes'));
        
        // Check if this automation is already queued for this recipient
        $this->db->where('rule_id', $rule->id);
        $this->db->where('recipient_type', $recipient['type']);
        $this->db->where('recipient_id', $recipient['id']);
        $this->db->where('status', 'pending');
        $existing = $this->db->get($this->queue_table)->row();
        
        if ($existing) {
            return false; // Already queued
        }
        
        // Add to queue
        $queue_data = [
            'rule_id' => $rule->id,
            'recipient_type' => $recipient['type'],
            'recipient_id' => $recipient['id'],
            'email' => $recipient['email'],
            'phone' => isset($recipient['phone']) ? $recipient['phone'] : null,
            'scheduled_at' => $scheduled_at,
            'status' => 'pending',
            'tracking_token' => $this->generate_tracking_token(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert($this->queue_table, $queue_data);
        
        return $this->db->insert_id();
    }
    
    /**
     * Get recipient information for trigger
     */
    private function get_recipient_for_trigger($trigger_type, $entity_id)
    {
        switch ($trigger_type) {
            case 'lead_created':
            case 'lead_status_changed':
                $this->db->select('id, email, phonenumber as phone, CONCAT(name, " ", lastname) as name');
                $this->db->where('id', $entity_id);
                $lead = $this->db->get(db_prefix() . 'leads')->row_array();
                
                if ($lead && $lead['email']) {
                    $lead['type'] = 'lead';
                    return $lead;
                }
                break;
                
            case 'customer_created':
                $this->db->select('userid as id, email, phonenumber as phone, company as name');
                $this->db->where('userid', $entity_id);
                $customer = $this->db->get(db_prefix() . 'clients')->row_array();
                
                if ($customer && $customer['email']) {
                    $customer['type'] = 'customer';
                    return $customer;
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Process automation queue
     */
    public function process_queue()
    {
        // Get pending queue items that are ready to be processed
        $this->db->where('status', 'pending');
        $this->db->where('scheduled_at <=', date('Y-m-d H:i:s'));
        $this->db->order_by('scheduled_at');
        $this->db->limit(50); // Process in batches
        $queue_items = $this->db->get($this->queue_table)->result();
        
        $processed = 0;
        
        foreach ($queue_items as $item) {
            if ($this->process_queue_item($item)) {
                $processed++;
            }
        }
        
        return $processed;
    }
    
    /**
     * Process single queue item
     */
    private function process_queue_item($item)
    {
        // Mark as processing
        $this->db->where('id', $item->id);
        $this->db->update($this->queue_table, [
            'status' => 'processing',
            'processed_at' => date('Y-m-d H:i:s')
        ]);
        
        // Get automation rule
        $rule = $this->get($item->rule_id);
        if (!$rule || !$rule->is_active) {
            $this->mark_queue_item_failed($item->id, 'Automation rule not found or inactive');
            return false;
        }
        
        // Process based on action type
        $success = false;
        $error_message = '';
        
        switch ($rule->action_type) {
            case 'send_email':
                $success = $this->process_send_email_action($rule, $item);
                break;
                
            case 'send_sms':
                $success = $this->process_send_sms_action($rule, $item);
                break;
                
            case 'add_to_campaign':
                $success = $this->process_add_to_campaign_action($rule, $item);
                break;
                
            case 'update_status':
                $success = $this->process_update_status_action($rule, $item);
                break;
                
            default:
                $error_message = 'Unknown action type: ' . $rule->action_type;
        }
        
        // Update queue item status
        if ($success) {
            $this->db->where('id', $item->id);
            $this->db->update($this->queue_table, ['status' => 'sent']);
        } else {
            $this->mark_queue_item_failed($item->id, $error_message);
        }
        
        return $success;
    }
    
    /**
     * Process send email action
     */
    private function process_send_email_action($rule, $item)
    {
        if (!$rule->template_id) {
            return false;
        }
        
        // Get template
        $this->load->model('templates_model');
        $template = $this->templates_model->get($rule->template_id);
        
        if (!$template) {
            return false;
        }
        
        // Create recipient object
        $recipient = (object) [
            'email' => $item->email,
            'phone' => $item->phone,
            'recipient_type' => $item->recipient_type,
            'recipient_id' => $item->recipient_id,
            'tracking_token' => $item->tracking_token
        ];
        
        // Send email
        $this->load->library('email_sender');
        return $this->email_sender->send_automation_email($rule, $recipient, $template);
    }
    
    /**
     * Process send SMS action
     */
    private function process_send_sms_action($rule, $item)
    {
        // SMS functionality would be implemented here
        // For now, return true as placeholder
        return true;
    }
    
    /**
     * Process add to campaign action
     */
    private function process_add_to_campaign_action($rule, $item)
    {
        $action_settings = json_decode($rule->action_settings, true);
        
        if (!isset($action_settings['campaign_id'])) {
            return false;
        }
        
        // Add recipient to campaign
        $this->load->model('campaigns_model');
        
        $recipient_data = [
            'campaign_id' => $action_settings['campaign_id'],
            'recipient_type' => $item->recipient_type,
            'recipient_id' => $item->recipient_id,
            'email' => $item->email,
            'phone' => $item->phone,
            'tracking_token' => $this->generate_tracking_token()
        ];
        
        $this->db->insert(db_prefix() . 'email_marketing_campaign_recipients', $recipient_data);
        
        return $this->db->insert_id() ? true : false;
    }
    
    /**
     * Process update status action
     */
    private function process_update_status_action($rule, $item)
    {
        $action_settings = json_decode($rule->action_settings, true);
        
        if (!isset($action_settings['new_status'])) {
            return false;
        }
        
        switch ($item->recipient_type) {
            case 'lead':
                $this->db->where('id', $item->recipient_id);
                return $this->db->update(db_prefix() . 'leads', ['status' => $action_settings['new_status']]);
                
            case 'customer':
                $this->db->where('userid', $item->recipient_id);
                return $this->db->update(db_prefix() . 'clients', ['active' => $action_settings['new_status']]);
                
            default:
                return false;
        }
    }
    
    /**
     * Mark queue item as failed
     */
    private function mark_queue_item_failed($item_id, $error_message)
    {
        $this->db->where('id', $item_id);
        $this->db->update($this->queue_table, [
            'status' => 'failed',
            'error_message' => $error_message
        ]);
    }
    
    /**
     * Generate tracking token
     */
    private function generate_tracking_token()
    {
        return bin2hex(random_bytes(16));
    }
    
    /**
     * Get automation statistics
     */
    public function get_automation_stats($rule_id = null)
    {
        if ($rule_id) {
            $this->db->where('rule_id', $rule_id);
        }
        
        $this->db->select('
            COUNT(*) as total_queued,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = "processing" THEN 1 ELSE 0 END) as processing_count,
            SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count
        ');
        
        return $this->db->get($this->queue_table)->row_array();
    }
    
    /**
     * Get total automation rules count
     */
    public function get_total_rules()
    {
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Get active automation rules count
     */
    public function get_active_rules_count()
    {
        $this->db->where('is_active', 1);
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Get automation queue items
     */
    public function get_queue_items($where = [], $limit = null, $offset = null)
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        
        $this->db->order_by('scheduled_at', 'DESC');
        return $this->db->get($this->queue_table)->result();
    }
    
    /**
     * Cancel queued automation
     */
    public function cancel_queued_automation($queue_id)
    {
        $this->db->where('id', $queue_id);
        $this->db->where('status', 'pending');
        return $this->db->update($this->queue_table, ['status' => 'cancelled']);
    }
    
    /**
     * Duplicate automation rule
     */
    public function duplicate($id)
    {
        $rule = $this->get($id);
        if (!$rule) {
            return false;
        }
        
        $new_data = [
            'name' => $rule->name . ' (Copy)',
            'description' => $rule->description,
            'trigger_type' => $rule->trigger_type,
            'trigger_conditions' => $rule->trigger_conditions,
            'action_type' => $rule->action_type,
            'action_settings' => $rule->action_settings,
            'template_id' => $rule->template_id,
            'delay_minutes' => $rule->delay_minutes,
            'is_active' => 0 // Start as inactive
        ];
        
        return $this->add($new_data);
    }
    
    /**
     * Clean up old queue items
     */
    public function cleanup_old_queue_items($days = 30)
    {
        $cutoff_date = date('Y-m-d H:i:s', strtotime('-' . $days . ' days'));
        
        $this->db->where('created_at <', $cutoff_date);
        $this->db->where_in('status', ['sent', 'failed', 'cancelled']);
        
        return $this->db->delete($this->queue_table);
    }
}

