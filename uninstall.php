<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Module Uninstallation Script
 */

$CI = &get_instance();

// Check if user wants to keep data
$keep_data = get_option('email_marketing_keep_data_on_uninstall');

if (!$keep_data) {
    // Drop all module tables
    email_marketing_drop_tables();
    
    // Remove module options
    email_marketing_remove_options();
}

// Set module as inactive
update_option('email_marketing_module_active', 0);

/**
 * Drop all module database tables
 */
function email_marketing_drop_tables()
{
    $CI = &get_instance();
    
    $tables = [
        'email_marketing_analytics',
        'email_marketing_automation_queue',
        'email_marketing_campaign_recipients',
        'email_marketing_campaigns',
        'email_marketing_automation_rules',
        'email_marketing_templates',
        'email_marketing_segments',
        'email_marketing_unsubscribes',
        'email_marketing_smtp_configs',
        'email_marketing_settings'
    ];
    
    foreach ($tables as $table) {
        if ($CI->db->table_exists(db_prefix() . $table)) {
            $CI->db->query('DROP TABLE `' . db_prefix() . $table . '`');
        }
    }
}

/**
 * Remove module options from the main options table
 */
function email_marketing_remove_options()
{
    $CI = &get_instance();
    
    $options = [
        'email_marketing_module_active',
        'email_marketing_enabled',
        'email_marketing_default_from_name',
        'email_marketing_default_from_email',
        'email_marketing_tracking_enabled',
        'email_marketing_gdpr_enabled',
        'email_marketing_unsubscribe_page',
        'email_marketing_batch_size',
        'email_marketing_batch_delay',
        'email_marketing_keep_data_on_uninstall'
    ];
    
    foreach ($options as $option) {
        $CI->db->where('name', $option);
        $CI->db->delete(db_prefix() . 'options');
    }
}

