<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Email Marketing Analytics Model
 */
class Analytics_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Get total emails sent
     */
    public function get_total_emails_sent()
    {
        $this->db->select('SUM(sent_count) as total');
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $query = $this->db->get();
        
        $result = $query->row();
        return $result ? (int) $result->total : 0;
    }
    
    /**
     * Get average open rate
     */
    public function get_average_open_rate()
    {
        $this->db->select('AVG(CASE WHEN sent_count > 0 THEN (opened_count / sent_count) * 100 ELSE 0 END) as avg_rate');
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $this->db->where('sent_count >', 0);
        $query = $this->db->get();
        
        $result = $query->row();
        return $result ? round($result->avg_rate, 2) : 0;
    }
    
    /**
     * Get average click rate
     */
    public function get_average_click_rate()
    {
        $this->db->select('AVG(CASE WHEN sent_count > 0 THEN (clicked_count / sent_count) * 100 ELSE 0 END) as avg_rate');
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $this->db->where('sent_count >', 0);
        $query = $this->db->get();
        
        $result = $query->row();
        return $result ? round($result->avg_rate, 2) : 0;
    }
    
    /**
     * Get dashboard chart data for the last 30 days
     */
    public function get_dashboard_chart_data($days = 30)
    {
        $data = [];
        $start_date = date('Y-m-d', strtotime("-{$days} days"));
        
        // Get daily email counts
        $this->db->select('DATE(created_at) as date, COUNT(*) as count');
        $this->db->from(db_prefix() . 'email_marketing_analytics');
        $this->db->where('event_type', 'sent');
        $this->db->where('created_at >=', $start_date);
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'ASC');
        
        $sent_data = $this->db->get()->result();
        
        // Get daily open counts
        $this->db->select('DATE(created_at) as date, COUNT(*) as count');
        $this->db->from(db_prefix() . 'email_marketing_analytics');
        $this->db->where('event_type', 'opened');
        $this->db->where('created_at >=', $start_date);
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'ASC');
        
        $opened_data = $this->db->get()->result();
        
        // Get daily click counts
        $this->db->select('DATE(created_at) as date, COUNT(*) as count');
        $this->db->from(db_prefix() . 'email_marketing_analytics');
        $this->db->where('event_type', 'clicked');
        $this->db->where('created_at >=', $start_date);
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'ASC');
        
        $clicked_data = $this->db->get()->result();
        
        // Format data for chart
        $chart_data = [
            'labels' => [],
            'sent' => [],
            'opened' => [],
            'clicked' => []
        ];
        
        // Create arrays indexed by date
        $sent_by_date = [];
        foreach ($sent_data as $row) {
            $sent_by_date[$row->date] = $row->count;
        }
        
        $opened_by_date = [];
        foreach ($opened_data as $row) {
            $opened_by_date[$row->date] = $row->count;
        }
        
        $clicked_by_date = [];
        foreach ($clicked_data as $row) {
            $clicked_by_date[$row->date] = $row->count;
        }
        
        // Fill in data for each day
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $chart_data['labels'][] = date('M j', strtotime($date));
            $chart_data['sent'][] = isset($sent_by_date[$date]) ? $sent_by_date[$date] : 0;
            $chart_data['opened'][] = isset($opened_by_date[$date]) ? $opened_by_date[$date] : 0;
            $chart_data['clicked'][] = isset($clicked_by_date[$date]) ? $clicked_by_date[$date] : 0;
        }
        
        return $chart_data;
    }
    
    /**
     * Track email open
     */
    public function track_email_open($tracking_token)
    {
        if (empty($tracking_token)) {
            return false;
        }
        
        // Get recipient info from tracking token
        $recipient = $this->get_recipient_by_tracking_token($tracking_token);
        if (!$recipient) {
            return false;
        }
        
        // Check if already tracked
        $this->db->where('tracking_token', $tracking_token);
        $this->db->where('event_type', 'opened');
        $existing = $this->db->get(db_prefix() . 'email_marketing_analytics')->row();
        
        if ($existing) {
            return true; // Already tracked
        }
        
        // Insert analytics record
        $data = [
            'campaign_id' => $recipient->campaign_id,
            'recipient_email' => $recipient->email,
            'event_type' => 'opened',
            'tracking_token' => $tracking_token,
            'ip_address' => $this->input->ip_address(),
            'user_agent' => $this->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert(db_prefix() . 'email_marketing_analytics', $data);
        
        // Update campaign recipient status
        $this->db->where('tracking_token', $tracking_token);
        $this->db->update(db_prefix() . 'email_marketing_campaign_recipients', [
            'status' => 'opened',
            'opened_at' => date('Y-m-d H:i:s')
        ]);
        
        // Update campaign opened count
        $this->db->set('opened_count', 'opened_count + 1', FALSE);
        $this->db->where('id', $recipient->campaign_id);
        $this->db->update(db_prefix() . 'email_marketing_campaigns');
        
        return true;
    }
    
    /**
     * Track email click
     */
    public function track_email_click($tracking_token)
    {
        if (empty($tracking_token)) {
            return false;
        }
        
        // Decode tracking token to get URL and recipient info
        $decoded = base64_decode($tracking_token);
        $data = json_decode($decoded, true);
        
        if (!$data || !isset($data['url']) || !isset($data['token'])) {
            return false;
        }
        
        $recipient = $this->get_recipient_by_tracking_token($data['token']);
        if (!$recipient) {
            return false;
        }
        
        // Insert analytics record
        $analytics_data = [
            'campaign_id' => $recipient->campaign_id,
            'recipient_email' => $recipient->email,
            'event_type' => 'clicked',
            'event_data' => json_encode(['url' => $data['url']]),
            'tracking_token' => $data['token'],
            'ip_address' => $this->input->ip_address(),
            'user_agent' => $this->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert(db_prefix() . 'email_marketing_analytics', $analytics_data);
        
        // Update campaign recipient status
        $this->db->where('tracking_token', $data['token']);
        $this->db->update(db_prefix() . 'email_marketing_campaign_recipients', [
            'status' => 'clicked',
            'clicked_at' => date('Y-m-d H:i:s')
        ]);
        
        // Update campaign clicked count
        $this->db->set('clicked_count', 'clicked_count + 1', FALSE);
        $this->db->where('id', $recipient->campaign_id);
        $this->db->update(db_prefix() . 'email_marketing_campaigns');
        
        return $data['url'];
    }
    
    /**
     * Get recipient by tracking token
     */
    private function get_recipient_by_tracking_token($token)
    {
        $this->db->select('*');
        $this->db->from(db_prefix() . 'email_marketing_campaign_recipients');
        $this->db->where('tracking_token', $token);
        
        return $this->db->get()->row();
    }
    
    /**
     * Get campaign analytics
     */
    public function get_campaign_analytics($campaign_id)
    {
        $analytics = [];
        
        // Get basic campaign stats
        $this->db->select('*');
        $this->db->from(db_prefix() . 'email_marketing_campaigns');
        $this->db->where('id', $campaign_id);
        $campaign = $this->db->get()->row();
        
        if (!$campaign) {
            return false;
        }
        
        $analytics['campaign'] = $campaign;
        
        // Get detailed event analytics
        $this->db->select('event_type, COUNT(*) as count');
        $this->db->from(db_prefix() . 'email_marketing_analytics');
        $this->db->where('campaign_id', $campaign_id);
        $this->db->group_by('event_type');
        
        $events = $this->db->get()->result();
        
        $analytics['events'] = [];
        foreach ($events as $event) {
            $analytics['events'][$event->event_type] = $event->count;
        }
        
        // Calculate rates
        if ($campaign->sent_count > 0) {
            $analytics['open_rate'] = round(($campaign->opened_count / $campaign->sent_count) * 100, 2);
            $analytics['click_rate'] = round(($campaign->clicked_count / $campaign->sent_count) * 100, 2);
            $analytics['bounce_rate'] = round(($campaign->bounced_count / $campaign->sent_count) * 100, 2);
            $analytics['unsubscribe_rate'] = round(($campaign->unsubscribed_count / $campaign->sent_count) * 100, 2);
        } else {
            $analytics['open_rate'] = 0;
            $analytics['click_rate'] = 0;
            $analytics['bounce_rate'] = 0;
            $analytics['unsubscribe_rate'] = 0;
        }
        
        return $analytics;
    }
}
