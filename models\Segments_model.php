<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Segments Model
 */
class Segments_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->table = db_prefix() . 'email_marketing_segments';
    }
    
    /**
     * Get all segments
     */
    public function get_all($where = [])
    {
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get single segment
     */
    public function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }
    
    /**
     * Add new segment
     */
    public function add($data)
    {
        $data['created_by'] = get_staff_user_id();
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Process conditions if provided as array
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        $this->db->insert($this->table, $data);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            // Calculate initial recipient count
            $this->calculate_segment_recipients($insert_id);
            
            log_activity('New Email Marketing Segment Created [ID: ' . $insert_id . ', Name: ' . $data['name'] . ']');
        }
        
        return $insert_id;
    }
    
    /**
     * Update segment
     */
    public function update($data, $id)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Process conditions if provided as array
        if (isset($data['conditions']) && is_array($data['conditions'])) {
            $data['conditions'] = json_encode($data['conditions']);
        }
        
        $this->db->where('id', $id);
        $result = $this->db->update($this->table, $data);
        
        if ($result) {
            // Recalculate recipient count
            $this->calculate_segment_recipients($id);
            
            log_activity('Email Marketing Segment Updated [ID: ' . $id . ']');
        }
        
        return $result;
    }
    
    /**
     * Delete segment
     */
    public function delete($id)
    {
        $segment = $this->get($id);
        if (!$segment) {
            return false;
        }
        
        $this->db->where('id', $id);
        $result = $this->db->delete($this->table);
        
        if ($result) {
            log_activity('Email Marketing Segment Deleted [ID: ' . $id . ', Name: ' . $segment->name . ']');
        }
        
        return $result;
    }
    
    /**
     * Get segment recipients
     */
    public function get_segment_recipients($id)
    {
        $segment = $this->get($id);
        if (!$segment) {
            return [];
        }
        
        $conditions = json_decode($segment->conditions, true);
        return $this->get_recipients_by_conditions($conditions, $segment->target_type);
    }
    
    /**
     * Get recipients by conditions and target type
     */
    public function get_recipients_by_conditions($conditions, $target_type = 'mixed')
    {
        $recipients = [];
        
        // Process leads
        if ($target_type == 'leads' || $target_type == 'mixed') {
            $leads = $this->get_leads_by_conditions($conditions);
            $recipients = array_merge($recipients, $leads);
        }
        
        // Process customers
        if ($target_type == 'customers' || $target_type == 'mixed') {
            $customers = $this->get_customers_by_conditions($conditions);
            $recipients = array_merge($recipients, $customers);
        }
        
        // Process contacts
        if ($target_type == 'contacts' || $target_type == 'mixed') {
            $contacts = $this->get_contacts_by_conditions($conditions);
            $recipients = array_merge($recipients, $contacts);
        }
        
        // Remove duplicates based on email
        return $this->remove_duplicate_emails($recipients);
    }
    
    /**
     * Get leads by conditions
     */
    private function get_leads_by_conditions($conditions)
    {
        $this->db->select('
            l.id,
            l.email,
            l.phonenumber as phone,
            CONCAT(l.name, " ", l.lastname) as name,
            "lead" as type,
            l.source,
            l.status,
            l.assigned,
            l.country,
            l.dateadded,
            l.lastcontact
        ');
        $this->db->from(db_prefix() . 'leads l');
        $this->db->where('l.email !=', '');
        
        // Apply lead source filter
        if (isset($conditions['lead_sources']) && !empty($conditions['lead_sources'])) {
            $this->db->where_in('l.source', $conditions['lead_sources']);
        }
        
        // Apply lead status filter
        if (isset($conditions['lead_statuses']) && !empty($conditions['lead_statuses'])) {
            $this->db->where_in('l.status', $conditions['lead_statuses']);
        }
        
        // Apply assigned staff filter
        if (isset($conditions['assigned_staff']) && !empty($conditions['assigned_staff'])) {
            $this->db->where_in('l.assigned', $conditions['assigned_staff']);
        }
        
        // Apply country filter
        if (isset($conditions['countries']) && !empty($conditions['countries'])) {
            $this->db->where_in('l.country', $conditions['countries']);
        }
        
        // Apply date range filter
        if (isset($conditions['date_added_from']) && $conditions['date_added_from']) {
            $this->db->where('l.dateadded >=', $conditions['date_added_from']);
        }
        
        if (isset($conditions['date_added_to']) && $conditions['date_added_to']) {
            $this->db->where('l.dateadded <=', $conditions['date_added_to']);
        }
        
        // Apply last contact filter
        if (isset($conditions['last_contact_from']) && $conditions['last_contact_from']) {
            $this->db->where('l.lastcontact >=', $conditions['last_contact_from']);
        }
        
        if (isset($conditions['last_contact_to']) && $conditions['last_contact_to']) {
            $this->db->where('l.lastcontact <=', $conditions['last_contact_to']);
        }
        
        // Apply custom field filters
        if (isset($conditions['custom_fields']) && !empty($conditions['custom_fields'])) {
            foreach ($conditions['custom_fields'] as $field_id => $field_value) {
                $this->db->join(db_prefix() . 'customfieldsvalues cfv', 'cfv.relid = l.id AND cfv.fieldto = "leads" AND cfv.fieldid = ' . (int)$field_id, 'left');
                $this->db->where('cfv.value', $field_value);
            }
        }
        
        return $this->db->get()->result_array();
    }
    
    /**
     * Get customers by conditions
     */
    private function get_customers_by_conditions($conditions)
    {
        $this->db->select('
            c.userid as id,
            c.email,
            c.phonenumber as phone,
            c.company as name,
            "customer" as type,
            c.country,
            c.datecreated,
            c.groups_in
        ');
        $this->db->from(db_prefix() . 'clients c');
        $this->db->where('c.email !=', '');
        
        // Apply customer group filter
        if (isset($conditions['customer_groups']) && !empty($conditions['customer_groups'])) {
            $group_conditions = [];
            foreach ($conditions['customer_groups'] as $group_id) {
                $group_conditions[] = 'FIND_IN_SET(' . (int)$group_id . ', c.groups_in)';
            }
            $this->db->where('(' . implode(' OR ', $group_conditions) . ')');
        }
        
        // Apply country filter
        if (isset($conditions['countries']) && !empty($conditions['countries'])) {
            $this->db->where_in('c.country', $conditions['countries']);
        }
        
        // Apply date created filter
        if (isset($conditions['date_created_from']) && $conditions['date_created_from']) {
            $this->db->where('c.datecreated >=', $conditions['date_created_from']);
        }
        
        if (isset($conditions['date_created_to']) && $conditions['date_created_to']) {
            $this->db->where('c.datecreated <=', $conditions['date_created_to']);
        }
        
        // Apply customer status filter
        if (isset($conditions['customer_active']) && $conditions['customer_active'] !== '') {
            $this->db->where('c.active', $conditions['customer_active']);
        }
        
        // Apply custom field filters
        if (isset($conditions['custom_fields']) && !empty($conditions['custom_fields'])) {
            foreach ($conditions['custom_fields'] as $field_id => $field_value) {
                $this->db->join(db_prefix() . 'customfieldsvalues cfv', 'cfv.relid = c.userid AND cfv.fieldto = "customers" AND cfv.fieldid = ' . (int)$field_id, 'left');
                $this->db->where('cfv.value', $field_value);
            }
        }
        
        return $this->db->get()->result_array();
    }
    
    /**
     * Get contacts by conditions
     */
    private function get_contacts_by_conditions($conditions)
    {
        $this->db->select('
            co.id,
            co.email,
            co.phonenumber as phone,
            CONCAT(co.firstname, " ", co.lastname) as name,
            "contact" as type,
            co.userid as customer_id,
            co.datecreated
        ');
        $this->db->from(db_prefix() . 'contacts co');
        $this->db->where('co.email !=', '');
        $this->db->where('co.active', 1);
        
        // Apply customer filter if specified
        if (isset($conditions['customer_ids']) && !empty($conditions['customer_ids'])) {
            $this->db->where_in('co.userid', $conditions['customer_ids']);
        }
        
        // Apply date created filter
        if (isset($conditions['date_created_from']) && $conditions['date_created_from']) {
            $this->db->where('co.datecreated >=', $conditions['date_created_from']);
        }
        
        if (isset($conditions['date_created_to']) && $conditions['date_created_to']) {
            $this->db->where('co.datecreated <=', $conditions['date_created_to']);
        }
        
        return $this->db->get()->result_array();
    }
    
    /**
     * Remove duplicate emails from recipients array
     */
    private function remove_duplicate_emails($recipients)
    {
        $unique_recipients = [];
        $emails = [];
        
        foreach ($recipients as $recipient) {
            if (!in_array($recipient['email'], $emails)) {
                $emails[] = $recipient['email'];
                $unique_recipients[] = $recipient;
            }
        }
        
        return $unique_recipients;
    }
    
    /**
     * Calculate and update segment recipient count
     */
    public function calculate_segment_recipients($id)
    {
        $recipients = $this->get_segment_recipients($id);
        $count = count($recipients);
        
        $this->db->where('id', $id);
        $this->db->update($this->table, [
            'recipient_count' => $count,
            'last_calculated' => date('Y-m-d H:i:s')
        ]);
        
        return $count;
    }
    
    /**
     * Get available lead sources
     */
    public function get_lead_sources()
    {
        $this->db->select('id, name');
        $this->db->from(db_prefix() . 'leads_sources');
        $this->db->order_by('name');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available lead statuses
     */
    public function get_lead_statuses()
    {
        $this->db->select('id, name, color');
        $this->db->from(db_prefix() . 'leads_status');
        $this->db->order_by('statusorder');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available customer groups
     */
    public function get_customer_groups()
    {
        $this->db->select('id, name');
        $this->db->from(db_prefix() . 'customers_groups');
        $this->db->order_by('name');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available staff members
     */
    public function get_staff_members()
    {
        $this->db->select('staffid as id, CONCAT(firstname, " ", lastname) as name');
        $this->db->from(db_prefix() . 'staff');
        $this->db->where('active', 1);
        $this->db->order_by('firstname, lastname');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get available countries
     */
    public function get_countries()
    {
        // Get unique countries from leads and customers
        $this->db->select('country');
        $this->db->from(db_prefix() . 'leads');
        $this->db->where('country !=', '');
        $this->db->group_by('country');
        $leads_countries = $this->db->get()->result_array();
        
        $this->db->select('country');
        $this->db->from(db_prefix() . 'clients');
        $this->db->where('country !=', '');
        $this->db->group_by('country');
        $customers_countries = $this->db->get()->result_array();
        
        $countries = array_merge($leads_countries, $customers_countries);
        $unique_countries = [];
        
        foreach ($countries as $country) {
            if (!in_array($country['country'], $unique_countries)) {
                $unique_countries[] = $country['country'];
            }
        }
        
        sort($unique_countries);
        return $unique_countries;
    }
    
    /**
     * Get custom fields for leads
     */
    public function get_lead_custom_fields()
    {
        $this->db->select('id, name, type');
        $this->db->from(db_prefix() . 'customfields');
        $this->db->where('fieldto', 'leads');
        $this->db->where('active', 1);
        $this->db->order_by('field_order');
        return $this->db->get()->result_array();
    }
    
    /**
     * Get custom fields for customers
     */
    public function get_customer_custom_fields()
    {
        $this->db->select('id, name, type');
        $this->db->from(db_prefix() . 'customfields');
        $this->db->where('fieldto', 'customers');
        $this->db->where('active', 1);
        $this->db->order_by('field_order');
        return $this->db->get()->result_array();
    }
    
    /**
     * Preview segment recipients (limited for performance)
     */
    public function preview_segment_recipients($id, $limit = 100)
    {
        $recipients = $this->get_segment_recipients($id);
        return array_slice($recipients, 0, $limit);
    }
    
    /**
     * Test segment conditions
     */
    public function test_segment_conditions($conditions, $target_type)
    {
        $recipients = $this->get_recipients_by_conditions($conditions, $target_type);
        return [
            'count' => count($recipients),
            'preview' => array_slice($recipients, 0, 10) // First 10 for preview
        ];
    }
    
    /**
     * Export segment recipients
     */
    public function export_segment_recipients($id)
    {
        $recipients = $this->get_segment_recipients($id);
        
        $export_data = [];
        foreach ($recipients as $recipient) {
            $export_data[] = [
                'name' => $recipient['name'],
                'email' => $recipient['email'],
                'phone' => $recipient['phone'],
                'type' => $recipient['type'],
                'source' => isset($recipient['source']) ? $recipient['source'] : '',
                'status' => isset($recipient['status']) ? $recipient['status'] : '',
                'country' => isset($recipient['country']) ? $recipient['country'] : ''
            ];
        }
        
        return $export_data;
    }
    
    /**
     * Get segment usage in campaigns
     */
    public function get_segment_usage($id)
    {
        // This would require additional tracking in campaigns
        // For now, return empty array
        return [];
    }
    
    /**
     * Duplicate segment
     */
    public function duplicate($id)
    {
        $segment = $this->get($id);
        if (!$segment) {
            return false;
        }
        
        $new_data = [
            'name' => $segment->name . ' (Copy)',
            'description' => $segment->description,
            'target_type' => $segment->target_type,
            'conditions' => $segment->conditions
        ];
        
        return $this->add($new_data);
    }
    
    /**
     * Get segments statistics
     */
    public function get_segments_stats()
    {
        $total_segments = $this->db->count_all_results($this->table);
        
        $this->db->select('SUM(recipient_count) as total_recipients');
        $result = $this->db->get($this->table)->row();
        $total_recipients = $result ? $result->total_recipients : 0;
        
        $this->db->select('target_type, COUNT(*) as count');
        $this->db->group_by('target_type');
        $by_type = $this->db->get($this->table)->result_array();
        
        return [
            'total_segments' => $total_segments,
            'total_recipients' => $total_recipients,
            'by_type' => $by_type
        ];
    }
}

