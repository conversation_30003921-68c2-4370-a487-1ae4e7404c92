<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Campaigns Controller
 */
class Campaigns extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        $this->load->model('email_marketing/campaigns_model');
        $this->load->model('email_marketing/templates_model');
        $this->load->model('email_marketing/segments_model');
        $this->lang->load('email_marketing');
        
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
    }
    
    /**
     * Get campaigns data for DataTables
     */
    public function table()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/campaigns/table'));
    }
    
    /**
     * Get campaign statistics
     */
    public function stats($id)
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $campaign = $this->campaigns_model->get($id);
        if (!$campaign) {
            show_404();
        }
        
        $stats = $this->campaigns_model->get_campaign_stats($id);
        
        header('Content-Type: application/json');
        echo json_encode($stats);
    }
    
    /**
     * Get campaign recipients for preview
     */
    public function preview_recipients()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $segment_id = $this->input->post('segment_id');
        $custom_conditions = $this->input->post('custom_conditions');
        
        if ($segment_id) {
            $recipients = $this->segments_model->get_segment_recipients($segment_id);
        } else if ($custom_conditions) {
            $recipients = $this->campaigns_model->get_recipients_by_conditions($custom_conditions);
        } else {
            $recipients = [];
        }
        
        $data['recipients'] = $recipients;
        $html = $this->load->view('admin/email_marketing/campaigns/recipients_preview', $data, true);
        
        echo json_encode(['html' => $html, 'count' => count($recipients)]);
    }
    
    /**
     * Load template content
     */
    public function load_template()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $template_id = $this->input->post('template_id');
        $template = $this->templates_model->get($template_id);
        
        if ($template) {
            echo json_encode([
                'success' => true,
                'subject' => $template->subject,
                'content' => $template->content
            ]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    
    /**
     * Save campaign as draft
     */
    public function save_draft()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'create') && !has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $data = $this->input->post();
        $data['status'] = 'draft';
        
        if (isset($data['id']) && $data['id']) {
            // Update existing campaign
            $success = $this->campaigns_model->update($data, $data['id']);
            $campaign_id = $data['id'];
        } else {
            // Create new campaign
            $campaign_id = $this->campaigns_model->add($data);
            $success = $campaign_id ? true : false;
        }
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'campaign_id' => $campaign_id,
                'message' => 'Campaign saved as draft'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to save campaign'
            ]);
        }
    }
    
    /**
     * Validate campaign before sending
     */
    public function validate_campaign()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $campaign_id = $this->input->post('campaign_id');
        $validation_result = $this->campaigns_model->validate_campaign($campaign_id);
        
        header('Content-Type: application/json');
        echo json_encode($validation_result);
    }
    
    /**
     * Get merge tags for template editor
     */
    public function get_merge_tags()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $CI = &get_instance();
        $CI->load->config('email_marketing/email_marketing_config');
        $merge_tags = $CI->config->item('email_marketing')['merge_tags'];
        
        header('Content-Type: application/json');
        echo json_encode($merge_tags);
    }
    
    /**
     * Process campaign queue (called by cron)
     */
    public function process_queue()
    {
        // This method should only be called by cron or authorized systems
        if (!$this->input->is_cli_request() && !$this->input->get('cron_key')) {
            show_404();
        }
        
        $processed = $this->campaigns_model->process_sending_queue();
        
        if ($this->input->is_cli_request()) {
            echo "Processed {$processed} campaigns\n";
        } else {
            echo json_encode(['processed' => $processed]);
        }
    }
    
    /**
     * Export campaign data
     */
    public function export($id)
    {
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
        
        $campaign = $this->campaigns_model->get($id);
        if (!$campaign) {
            show_404();
        }
        
        $export_data = $this->campaigns_model->export_campaign_data($id);
        
        $filename = 'campaign_' . $id . '_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Write CSV headers
        fputcsv($output, array_keys($export_data[0]));
        
        // Write data rows
        foreach ($export_data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
    }
    
    /**
     * Clone campaign with new name
     */
    public function clone_campaign()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'create')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $campaign_id = $this->input->post('campaign_id');
        $new_name = $this->input->post('new_name');
        
        $new_campaign_id = $this->campaigns_model->clone_campaign($campaign_id, $new_name);
        
        if ($new_campaign_id) {
            echo json_encode([
                'success' => true,
                'campaign_id' => $new_campaign_id,
                'message' => 'Campaign cloned successfully'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Failed to clone campaign'
            ]);
        }
    }
}

