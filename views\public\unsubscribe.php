<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe - <?php echo htmlspecialchars($company_name); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .success-message::before {
            content: "✓";
            background: #28a745;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .error-message::before {
            content: "!";
            background: #dc3545;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .radio-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .radio-option input[type="radio"] {
            margin-right: 12px;
            transform: scale(1.2);
        }
        
        .radio-option.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option-content {
            flex: 1;
        }
        
        .option-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .option-description {
            font-size: 14px;
            color: #666;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .footer-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .info-text {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
            }
            
            .header, .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Unsubscribe</h1>
            <p>Manage your email preferences</p>
        </div>
        
        <div class="content">
            <?php if ($processed && isset($result)): ?>
                <?php if ($result['success']): ?>
                    <div class="success-message">
                        <div>
                            <?php if (isset($result['already_unsubscribed']) && $result['already_unsubscribed']): ?>
                                You are already unsubscribed from our emails.
                            <?php else: ?>
                                You have been successfully unsubscribed from our emails.
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-text">
                        <strong>What happens next?</strong><br>
                        • You will no longer receive the selected types of emails<br>
                        • This change is effective immediately<br>
                        • You can resubscribe at any time using the link below
                    </div>
                    
                    <div class="footer-links">
                        <a href="<?php echo site_url('email_marketing/resubscribe?token=' . $token); ?>">Resubscribe</a>
                        <a href="<?php echo site_url('email_marketing/preferences?token=' . $token); ?>">Manage Preferences</a>
                        <a href="<?php echo site_url('email_marketing/privacy'); ?>">Privacy Policy</a>
                    </div>
                <?php else: ?>
                    <div class="error-message">
                        <div><?php echo htmlspecialchars($result['message']); ?></div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="error-message">
                        <div><?php echo htmlspecialchars($error); ?></div>
                    </div>
                <?php endif; ?>
                
                <div class="info-text">
                    We're sorry to see you go! Please select what type of emails you'd like to unsubscribe from below.
                </div>
                
                <form method="post" action="">
                    <div class="form-group">
                        <label>What would you like to unsubscribe from?</label>
                        <div class="radio-group">
                            <label class="radio-option" onclick="selectOption(this)">
                                <input type="radio" name="unsubscribe_type" value="all" <?php echo $type == 'all' ? 'checked' : ''; ?>>
                                <div class="option-content">
                                    <div class="option-title">All Emails</div>
                                    <div class="option-description">Unsubscribe from all marketing and promotional emails</div>
                                </div>
                            </label>
                            
                            <label class="radio-option" onclick="selectOption(this)">
                                <input type="radio" name="unsubscribe_type" value="marketing" <?php echo $type == 'marketing' ? 'checked' : ''; ?>>
                                <div class="option-content">
                                    <div class="option-title">Marketing Emails</div>
                                    <div class="option-description">Unsubscribe from marketing and promotional content only</div>
                                </div>
                            </label>
                            
                            <label class="radio-option" onclick="selectOption(this)">
                                <input type="radio" name="unsubscribe_type" value="newsletter" <?php echo $type == 'newsletter' ? 'checked' : ''; ?>>
                                <div class="option-content">
                                    <div class="option-title">Newsletter</div>
                                    <div class="option-description">Unsubscribe from our newsletter only</div>
                                </div>
                            </label>
                            
                            <label class="radio-option" onclick="selectOption(this)">
                                <input type="radio" name="unsubscribe_type" value="promotions" <?php echo $type == 'promotions' ? 'checked' : ''; ?>>
                                <div class="option-content">
                                    <div class="option-title">Promotions</div>
                                    <div class="option-description">Unsubscribe from promotional offers and deals</div>
                                </div>
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">Unsubscribe</button>
                </form>
                
                <div class="footer-links">
                    <a href="<?php echo site_url('email_marketing/preferences?token=' . $token); ?>">Manage Preferences Instead</a>
                    <a href="<?php echo site_url('email_marketing/privacy'); ?>">Privacy Policy</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function selectOption(element) {
            // Remove selected class from all options
            document.querySelectorAll('.radio-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selected class to clicked option
            element.classList.add('selected');
            
            // Check the radio button
            element.querySelector('input[type="radio"]').checked = true;
        }
        
        // Initialize selected state
        document.addEventListener('DOMContentLoaded', function() {
            const checkedRadio = document.querySelector('input[type="radio"]:checked');
            if (checkedRadio) {
                checkedRadio.closest('.radio-option').classList.add('selected');
            }
        });
    </script>
</body>
</html>

