<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Automation Controller
 */
class Automation extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        
        $this->load->model('automation_model');
        $this->load->model('templates_model');
        $this->load_module_language();
        
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
    }

    /**
     * Load module language file
     */
    private function load_module_language()
    {
        $language = $this->config->item('language');
        if (empty($language)) {
            $language = 'english';
        }

        $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/' . $language . '/email_marketing_lang.php';

        if (file_exists($lang_file)) {
            $this->lang->load('email_marketing_lang', $language, FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
        } else {
            // Fallback to english
            $lang_file = EMAIL_MARKETING_MODULE_PATH . '/language/english/email_marketing_lang.php';
            if (file_exists($lang_file)) {
                $this->lang->load('email_marketing_lang', 'english', FALSE, TRUE, EMAIL_MARKETING_MODULE_PATH . '/');
            }
        }
    }
    
    /**
     * Automation rules listing
     */
    public function index()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/automation/table'));
        }
        
        $data['title'] = _l('email_marketing_automation');
        $this->load->view('admin/email_marketing/automation/manage', $data);
    }
    
    /**
     * Create/Edit automation rule
     */
    public function rule($id = '')
    {
        if ($this->input->post()) {
            if ($id == '') {
                // Create new rule
                if (!has_permission('email_marketing', '', 'create')) {
                    access_denied('email_marketing');
                }
                
                $rule_id = $this->automation_model->add($this->input->post());
                
                if ($rule_id) {
                    set_alert('success', _l('email_marketing_automation_created'));
                    redirect(admin_url('email_marketing/automation/rule/' . $rule_id));
                } else {
                    set_alert('danger', 'Failed to create automation rule.');
                }
            } else {
                // Update existing rule
                if (!has_permission('email_marketing', '', 'edit')) {
                    access_denied('email_marketing');
                }
                
                $success = $this->automation_model->update($this->input->post(), $id);
                
                if ($success) {
                    set_alert('success', _l('email_marketing_automation_updated'));
                } else {
                    set_alert('danger', 'Failed to update automation rule.');
                }
                
                redirect(admin_url('email_marketing/automation/rule/' . $id));
            }
        }
        
        if ($id == '') {
            $title = _l('email_marketing_new_automation_rule');
            $rule = null;
        } else {
            $rule = $this->automation_model->get($id);
            if (!$rule) {
                show_404();
            }
            $title = _l('email_marketing_edit_automation_rule');
        }
        
        // Get available templates
        $data['templates'] = $this->templates_model->get_by_type('automation');
        
        // Get trigger options
        $data['trigger_options'] = $this->get_trigger_options();
        
        $data['rule'] = $rule;
        $data['title'] = $title;
        $this->load->view('admin/email_marketing/automation/rule', $data);
    }
    
    /**
     * Delete automation rule
     */
    public function delete($id)
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        if (!$id) {
            redirect(admin_url('email_marketing/automation'));
        }
        
        $response = $this->automation_model->delete($id);
        
        if ($response === true) {
            set_alert('success', _l('email_marketing_automation_deleted'));
        } else {
            set_alert('warning', 'Failed to delete automation rule.');
        }
        
        redirect(admin_url('email_marketing/automation'));
    }
    
    /**
     * Activate automation rule
     */
    public function activate($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $success = $this->automation_model->activate($id);
        
        if ($success) {
            set_alert('success', _l('email_marketing_automation_activated'));
        } else {
            set_alert('danger', 'Failed to activate automation rule.');
        }
        
        redirect(admin_url('email_marketing/automation'));
    }
    
    /**
     * Deactivate automation rule
     */
    public function deactivate($id)
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $success = $this->automation_model->deactivate($id);
        
        if ($success) {
            set_alert('success', _l('email_marketing_automation_deactivated'));
        } else {
            set_alert('danger', 'Failed to deactivate automation rule.');
        }
        
        redirect(admin_url('email_marketing/automation'));
    }
    
    /**
     * Duplicate automation rule
     */
    public function duplicate($id)
    {
        if (!has_permission('email_marketing', '', 'create')) {
            access_denied('email_marketing');
        }
        
        $new_rule_id = $this->automation_model->duplicate($id);
        
        if ($new_rule_id) {
            set_alert('success', 'Automation rule duplicated successfully.');
            redirect(admin_url('email_marketing/automation/rule/' . $new_rule_id));
        } else {
            set_alert('danger', 'Failed to duplicate automation rule.');
            redirect(admin_url('email_marketing/automation'));
        }
    }
    
    /**
     * Automation queue management
     */
    public function queue()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/automation/queue_table'));
        }
        
        $data['title'] = _l('email_marketing_automation_queue');
        $data['stats'] = $this->automation_model->get_automation_stats();
        $this->load->view('admin/automation/queue', $data);
    }
    
    /**
     * Get automation statistics
     */
    public function stats($id = null)
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $stats = $this->automation_model->get_automation_stats($id);
        
        header('Content-Type: application/json');
        echo json_encode($stats);
    }
    
    /**
     * Process automation queue manually
     */
    public function process_queue()
    {
        if (!has_permission('email_marketing', '', 'edit')) {
            access_denied('email_marketing');
        }
        
        $processed = $this->automation_model->process_queue();
        
        set_alert('success', "Processed {$processed} automation items.");
        redirect(admin_url('email_marketing/automation/queue'));
    }
    
    /**
     * Cancel queued automation
     */
    public function cancel_queue_item()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $queue_id = $this->input->post('queue_id');
        $success = $this->automation_model->cancel_queued_automation($queue_id);
        
        if ($success) {
            echo json_encode(['success' => true, 'message' => 'Automation cancelled successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to cancel automation']);
        }
    }
    
    /**
     * Get trigger condition options
     */
    public function get_trigger_conditions()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $trigger_type = $this->input->post('trigger_type');
        $conditions = $this->get_conditions_for_trigger($trigger_type);
        
        header('Content-Type: application/json');
        echo json_encode($conditions);
    }
    
    /**
     * Get action setting options
     */
    public function get_action_settings()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $action_type = $this->input->post('action_type');
        $settings = $this->get_settings_for_action($action_type);
        
        header('Content-Type: application/json');
        echo json_encode($settings);
    }
    
    /**
     * Test automation rule
     */
    public function test_rule()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        if (!has_permission('email_marketing', '', 'edit')) {
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            return;
        }
        
        $rule_id = $this->input->post('rule_id');
        $test_email = $this->input->post('test_email');
        
        $rule = $this->automation_model->get($rule_id);
        if (!$rule) {
            echo json_encode(['success' => false, 'message' => 'Automation rule not found']);
            return;
        }
        
        // Create test recipient
        $test_recipient = (object) [
            'email' => $test_email,
            'name' => 'Test User',
            'recipient_type' => 'test',
            'recipient_id' => 0,
            'tracking_token' => 'test_' . uniqid()
        ];
        
        // Get template
        $template = $this->templates_model->get($rule->template_id);
        if (!$template) {
            echo json_encode(['success' => false, 'message' => 'Template not found']);
            return;
        }
        
        // Send test email
        $this->load->library('email_marketing/email_sender');
        $result = $this->email_sender->send_automation_email($rule, $test_recipient, $template);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Test email sent successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => $this->email_sender->get_last_error()]);
        }
    }
    
    /**
     * Get trigger options for form
     */
    private function get_trigger_options()
    {
        return [
            'lead_created' => [
                'name' => _l('email_marketing_trigger_lead_created'),
                'conditions' => ['sources', 'statuses', 'assigned_staff', 'countries']
            ],
            'lead_status_changed' => [
                'name' => _l('email_marketing_trigger_lead_status_changed'),
                'conditions' => ['old_status', 'new_status', 'sources', 'assigned_staff', 'countries']
            ],
            'customer_created' => [
                'name' => _l('email_marketing_trigger_customer_created'),
                'conditions' => ['groups', 'countries']
            ],
            'custom_date' => [
                'name' => _l('email_marketing_trigger_custom_date'),
                'conditions' => ['date', 'time']
            ],
            'manual' => [
                'name' => _l('email_marketing_trigger_manual'),
                'conditions' => []
            ]
        ];
    }
    
    /**
     * Get conditions for specific trigger type
     */
    private function get_conditions_for_trigger($trigger_type)
    {
        $this->load->model('email_marketing/segments_model');
        
        $conditions = [];
        
        switch ($trigger_type) {
            case 'lead_created':
            case 'lead_status_changed':
                $conditions['sources'] = $this->segments_model->get_lead_sources();
                $conditions['statuses'] = $this->segments_model->get_lead_statuses();
                $conditions['assigned_staff'] = $this->segments_model->get_staff_members();
                $conditions['countries'] = $this->segments_model->get_countries();
                break;
                
            case 'customer_created':
                $conditions['groups'] = $this->segments_model->get_customer_groups();
                $conditions['countries'] = $this->segments_model->get_countries();
                break;
                
            case 'custom_date':
                $conditions['date_fields'] = ['date', 'time'];
                break;
        }
        
        return $conditions;
    }
    
    /**
     * Get settings for specific action type
     */
    private function get_settings_for_action($action_type)
    {
        $settings = [];
        
        switch ($action_type) {
            case 'send_email':
                $settings['templates'] = $this->templates_model->get_by_type('automation');
                break;
                
            case 'send_sms':
                $settings['sms_templates'] = []; // SMS templates would go here
                break;
                
            case 'add_to_campaign':
                $this->load->model('email_marketing/campaigns_model');
                $settings['campaigns'] = $this->campaigns_model->get_all(['status' => 'draft']);
                break;
                
            case 'update_status':
                $this->load->model('email_marketing/segments_model');
                $settings['lead_statuses'] = $this->segments_model->get_lead_statuses();
                $settings['customer_statuses'] = [
                    ['id' => 1, 'name' => 'Active'],
                    ['id' => 0, 'name' => 'Inactive']
                ];
                break;
        }
        
        return $settings;
    }
    
    /**
     * Export automation data
     */
    public function export($id)
    {
        if (!has_permission('email_marketing', '', 'view')) {
            access_denied('email_marketing');
        }
        
        $rule = $this->automation_model->get($id);
        if (!$rule) {
            show_404();
        }
        
        // Get queue items for this rule
        $queue_items = $this->automation_model->get_queue_items(['rule_id' => $id]);
        
        $export_data = [];
        foreach ($queue_items as $item) {
            $export_data[] = [
                'email' => $item->email,
                'recipient_type' => $item->recipient_type,
                'status' => $item->status,
                'scheduled_at' => $item->scheduled_at,
                'processed_at' => $item->processed_at,
                'error_message' => $item->error_message
            ];
        }
        
        $filename = 'automation_' . $id . '_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Write CSV headers
        if (!empty($export_data)) {
            fputcsv($output, array_keys($export_data[0]));
            
            // Write data rows
            foreach ($export_data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
    }
    
    /**
     * Automation history/logs
     */
    public function history($id)
    {
        $rule = $this->automation_model->get($id);
        if (!$rule) {
            show_404();
        }
        
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path(EMAIL_MARKETING_MODULE_NAME, 'admin/automation/history_table'));
        }
        
        $data['rule'] = $rule;
        $data['title'] = _l('email_marketing_automation_history') . ' - ' . $rule->name;
        $this->load->view('admin/automation/history', $data);
    }
    
    /**
     * Clean up old automation data
     */
    public function cleanup()
    {
        if (!has_permission('email_marketing', '', 'delete')) {
            access_denied('email_marketing');
        }
        
        $days = $this->input->post('days') ?: 30;
        $deleted = $this->automation_model->cleanup_old_queue_items($days);
        
        set_alert('success', "Cleaned up {$deleted} old automation records.");
        redirect(admin_url('email_marketing/automation'));
    }
}

